<template>
  <a-page-header style="background-color: white" :title="title">
    <template #tags>
      <a-upload
        :showUploadList="false"
        :before-upload="beforeUpload"
        :customRequest="handleUpload"
      >
        <a-button> 导入配置 </a-button>
      </a-upload>
    </template>
    <template #extra>
      <a-space wrap>
        <span
          v-html="dictTemplate.tabletempInt('projectProgress', projectProgress)"
        ></span>
        <a-button
          :type="activeButton == 1 ? 'primary' : 'default'"
          @click="activeButton = 1"
          >传感器配置</a-button
        >
        <a-button
          :type="activeButton == 2 ? 'primary' : 'default'"
          @click="activeButton = 2"
        >
          辐射区</a-button
        >
        <a-button
          :type="activeButton == 3 ? 'primary' : 'default'"
          @click="activeButton = 3"
          >新风区</a-button
        >
        <a-button
          :type="activeButton == 4 ? 'primary' : 'default'"
          @click="activeButton = 4"
          >冷热源</a-button
        >
        <a-button
          :type="activeButton == 5 ? 'primary' : 'default'"
          key="5"
          @click="activeButton = 5"
          >端口信息</a-button
        >
        <a-button
          :type="activeButton == 6 ? 'primary' : 'default'"
          key="6"
          @click="tjsave"
          >调试</a-button
        >
        <a-button
          v-if="activeButton == 6"
          :style="{ backgroundColor: '#52c41a', color: '#fff' }"
          @click="save"
          >运行</a-button
        >
      </a-space>
    </template>
  </a-page-header>
  <div style="padding-top: 8px">
    <SensorConfigPage
      v-if="activeButton == 1"
      :recordwhere="recordwhere1"
      ref="childPageRef"
    />
    <RadiationZonePage
      v-if="activeButton == 2"
      :recordwhere="recordwhere1"
      ref="childPageRef"
    />
    <FreshAirAreaPage
      v-if="activeButton == 3"
      :recordwhere="recordwhere1"
      ref="childPageRef"
    />
    <ColdHeatSourcesPage
      v-if="activeButton == 4"
      :recordwhere="recordwhere1"
      ref="childPageRef"
    />

    <PortPage
      v-if="activeButton == 5"
      :recordwhere="recordwhere1"
      ref="childPageRef"
    />
    <ConfigTestPage
      v-if="activeButton == 6"
      :recordwhere="recordwhere1"
      ref="childPageRef"
    />
  </div>
</template>
<script setup>
import { ref, provide, watch, inject } from 'vue'
import SensorConfigPage from '../../components/pages/projectConfig/SensorConfigPage.vue'
import RadiationZonePage from '../../components/pages/projectConfig/RadiationZonePage.vue'
import PortPage from '../../components/pages/projectConfig/PortPage.vue'
import FreshAirAreaPage from '../../components/pages/projectConfig/FreshAirAreaPage.vue'
import ColdHeatSourcesPage from '../../components/pages/projectConfig/ColdHeatSourcesPage.vue'
import ConfigTestPage from '../../components/pages/projectConfig/ConfigTestPage.vue'
import { Modal, message } from 'ant-design-vue'
import pcport from '@/api/methods/pcport'
import project from '@/api/methods/project'
import dictTemplate from '@/utils/dictTemplate'
// 注入父组件的方法
const configMaincancelload = inject('configMaincancelload')

const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const childPageRef = ref(null)
const activeButton = ref(1)
const projectProgress = ref(0)
// 响应式数据，用于存储面包屑项
const recordwhere1 = ref({
  projectId: props.recordwhere.projectId,
})
watch(activeButton, async (newQuestion, oldQuestion) => {
  switch (newQuestion) {
    case 1:
      recordwhere1.value = { projectId: props.recordwhere.projectId }

      break
    case 2:
      recordwhere1.value = {
        projectId: props.recordwhere.projectId,
        zoneType: 1,
      }
      break
    case 3:
      recordwhere1.value = { projectId: props.recordwhere.projectId }
      break
    case 4:
      recordwhere1.value = {
        projectId: props.recordwhere.projectId,
        coldHeatSourcesType: 1,
      }
      break
    case 5:
      recordwhere1.value = { projectId: props.recordwhere.projectId }
    case 6:
      recordwhere1.value = { projectId: props.recordwhere.projectId }
      break
  }
})
const activeButtonshow = ref(false)
const save = () => {
  Modal.confirm({
    title: '提示',
    content: '确定要保存运行吗？',
    onOk() {
      pcport.save({ projectId: props.recordwhere.projectId }).then(res => {
        message.success({
          content: '成功',
          duration: 1,
          onClose: () => {
            configMaincancelload()
          },
        })
      })
    },
  })
}
const getproject = () => {
  project.get({ id: props.recordwhere.projectId }).then(res => {
    projectProgress.value = res.data.projectProgress
  })
}
getproject()
const tjsave = () => {
  Modal.confirm({
    title: '提示',
    content: '确定要调试吗？',
    onOk() {
      if (projectProgress.value == 6 || projectProgress.value == 5) {
        activeButton.value = 6
      } else {
        pcport
          .portSaveInspect({ projectId: props.recordwhere.projectId })
          .then(res => {
            message.success({
              content: '成功',
              duration: 1,
              onClose: () => {
                activeButton.value = 6
                getproject()
              },
            })
          })
      }
    },
  })
}
// 上传前的校验（可选）
const beforeUpload = file => {
  // 检查文件扩展名
  const extension = file.name.split('.').pop().toLowerCase()

  // 同时检查MIME类型（注意：不同浏览器可能返回不同的MIME类型）
  const validMimeTypes = ['text/plain', 'application/octet-stream']

  if (extension !== 'txt' || !validMimeTypes.includes(file.type)) {
    message.error('只能上传TXT文本文件!')
    return false
  }

  // 限制文件大小（例如不超过2MB）
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    message.error('文件大小不能超过2MB!')
    return false
  }

  return true
}

const handleUpload = async ({ file, onSuccess, onError }) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('projectId', props.recordwhere.projectId)

  project.uploadConfigFile(formData).then(res => {
    message.success({
      content: '成功',
      duration: 1,
      onClose: () => {
        // if (configMaincancelload) {
        //   configMaincancelload()
        // }
        childPageRef.value.refreshData()
      },
    })
  })
}
provide('getproject', getproject)
</script>
