<template>
  <form-table
    :columns="columns"
    modulePath="sensorConfig"
    method="get"
    pageAction="getList"
    :where="recordwhere"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    :page="false"
    @edit="edit"
    ref="childRef"
  >
    <template #custom-sensorNo="{ record }">
      {{ record.prefix + record.sensorNo }}
    </template>
    <template #custom-roomType="{ record }">
      <span
        v-html="dictTemplate.tabletempInt('roomType', record.roomType)"
      ></span>
    </template>
    <template #custom-icon="{ record }">
      <img :src="record.icon" style="width: 25px" />
    </template>
  </form-table>
  <!-- 编辑 -->
  <SensorConfigEdit
    :editTitle="editTitle"
    :open="editopen"
    :formInfo="formInfo"
    @close="editopen = false"
    @updateData="refreshData"
  >
  </SensorConfigEdit>
</template>
<script setup>
import { ref, defineProps, reactive, toRaw, inject } from 'vue'
import FormTable from '../../FormTable.vue'
import SensorConfigEdit from '../../edit/SensorConfigEdit.vue'
import sensorConfig from '@/api/methods/sensorConfig'
import dictTemplate from '@/utils/dictTemplate'
// 注入父组件的方法
const getproject = inject('getproject')

const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const columns = ref([
  {
    title: '传感器编号',
    key: 'sensorNo',
  },
  {
    title: '房间名称',
    dataIndex: 'roomName',
  },
  {
    title: '房间类型',
    key: 'roomType',
  },
  {
    title: '楼层',
    dataIndex: 'floor',
  },
  {
    title: '楼层面积',
    dataIndex: 'roomSize',
  },
  {
    title: '图标',
    key: 'icon',
    dataIndex: 'icon',
  },

  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)
//
//编辑
const defaultformInfo = {
  id: '',
  projectId: props.recordwhere.projectId,
  sensorNo: 0,
  roomName: '',
  roomType: null,
  floor: 0,
  roomSize: 0,
  icon: '',
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editopen = ref(false)
const editTitle = ref('新增')
const edit = record => {
  // 触发自定义事件，父组件会监听这个事件
  if (record.id) {
    editTitle.value = '修改'
    sensorConfig.get({ id: record.id }).then(res => {
      Object.assign(formInfo, res.data)
      editopen.value = true
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
    editopen.value = true
  }
}
const refreshData = () => {
  childRef.value.tableLoad()
  getproject()
}
defineExpose({
  refreshData,
})
</script>
