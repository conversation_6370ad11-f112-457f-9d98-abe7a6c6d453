<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="sysDealer"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    @edit="edit"
    ref="childRef"
  >
  </form-table>
  <!-- 新增修改 -->
  <SysDealerEdit
    :open="editopen"
    @close="editopen = false"
    @updateData="refreshData"
    ref="editRef"
  >
  </SysDealerEdit>
</template>
<script setup>
import { ref } from 'vue'
import FormTable from '../../components/FormTable.vue'
import SysDealerEdit from '../../components/edit/SysDealerEdit.vue'

const formState = ref({
  name: { label: '名称', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
  },
  {
    title: '经销商名称',
    dataIndex: 'dealerName',
  },
  {
    title: '地址',
    dataIndex: 'address',
  },
  {
    title: '联系人',
    dataIndex: 'contacts',
  },
  {
    title: '联系电话',
    dataIndex: 'contactNumber',
  },
  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 250,
  },
])
const childRef = ref(null)

//编辑
const editopen = ref(false)
const editRef = ref(null)

const edit = record => {
  editRef.value.init(record.id)
  editopen.value = true
}
const refreshData = () => {
  childRef.value.tableLoad()
}
</script>

<style></style>
