import { get, post } from '@/api/request'

const pcport = {
  //根据id获取信息
  getPCPortbyProjectId(params) {
    return get('pcport/getPCPortbyProjectId', params)
  },
  pcPortList(params) {
    return post('pcport/pcPortList', params)
  },
  //释放端口
  releasePort(params) {
    return get('pcport/releasePort', params, true)
  },
  //删除
  deletePort(params) {
    return get('pcport/deletePort', params, true)
  },
  //保存端口
  save(params) {
    return get('pcport/save', params, true)
  },
  //端口保存检查测试状态
  portSaveInspect(params) {
    return get('pcport/portSaveInspect', params, true)
  },

  //创建测试状态
  createTest(params) {
    return get('pcport/createTest', params)
  },
  //下发查询指令
  selectHex(params) {
    return get('pcport/SelectHex', params)
  },
  //下发查询指令
  portDebugStatus(params) {
    return get('pcport/PortDebugStatus', params)
  },
}
//
export default pcport
