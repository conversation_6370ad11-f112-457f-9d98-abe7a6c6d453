<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="project"
    pageAction="selectProjectPage"
    :rowSelect="false"
    ref="childRef"
  >
    <template #custom-uid="{ record }">
      <div class="flex-row" v-if="editableData[record.id]">
        <div class="flex-item-8">
          <a-input v-model:value="record.uid" @pressEnter="save(record)" />
        </div>
        <div class="flex-item-2 padding-left-20">
          <a class="text-color-black" @click="save(record)">
            <check-outlined />
          </a>
        </div>
      </div>
      <div class="flex-row" v-else>
        <div class="flex-item-8">{{ record.uid }}</div>
        <div class="flex-item-2 padding-left-20">
          <a class="text-color-black" @click="edit(record)">
            <edit-outlined />
          </a>
        </div>
      </div>
    </template>
    <template #custom-provincecitydistrict="{ record }">
      {{
        record.province + record.city + record.district + record.detailedAddress
      }}
    </template>
    <template #custom-projectProgress="{ record }">
      <span
        v-html="
          dictTemplate.tabletempInt('projectProgress', record.projectProgress)
        "
      ></span>
    </template>
    <template #custom-operation="{ record }">
      <a @click="config(record)">配置</a>
      <a-divider type="vertical" />
      <a @click="downloadConfig(record)">下载配置文件</a>
    </template>
  </form-table>
  <a-modal
    v-model:open="configMain"
    :title="configMaintitle"
    width="90%"
    wrap-class-name="full-modal1"
    style="top: 5%"
    :maskClosable="false"
    :destroyOnClose="true"
    @cancel="closeconfigMain"
  >
    <ConfigMain :recordwhere="recordwhere"></ConfigMain>
    <template #footer>
      <a-button key="submit" type="primary" @click="closeconfigMain"
        >关闭</a-button
      >
    </template>
  </a-modal>
</template>
<script setup>
import { ref, reactive, provide, inject } from 'vue'
import FormTable from '../../components/FormTable.vue'
import ConfigMain from './configMain.vue'
import dictTemplate from '@/utils/dictTemplate'
import project from '@/api/methods/project'
import { message, Modal } from 'ant-design-vue'
import { saveAs } from 'file-saver'
const userInfo = inject('userInfo')
const formState = ref({
  projectName: { label: '项目名称', value: '', type: 'text' },
  uid: { label: 'UID', value: '', type: 'text' },
  communityName: { label: '小区名称', value: '', type: 'text' },
  status: {
    label: '状态',
    value: null,
    type: 'select',
    data: [
      { value: 1, label: '已运行' },
      { value: 0, label: '未运行' },
    ],
  },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '项目进度',
    key: 'projectProgress',
    width: 100,
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    width: 250,
  },
  {
    title: '唯一标识UID',
    key: 'uid',
    width: 200,
  },
  {
    title: '隶属小区',
    dataIndex: 'communityName',
    width: 200,
  },
  {
    title: '地址',
    key: 'provincecitydistrict',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const editableData = reactive({})
const edit = record => {
  editableData[record.id] = record
}
const save = record => {
  console.log(record)
  project
    .updateUid({
      id: record.id,
      uid: record.uid,
    })
    .then(res => {
      message.success('成功', 1, () => {
        editableData[record.id] = null
      })
    })
}
const childRef = ref(null)
const configMain = ref(false)
const configMaintitle = ref('')
const recordwhere = ref({})
const config = record => {
  if (!userInfo.value.isConfig) {
    message.error('暂无配置权限，无法进行操作')
    return
  }
  if (record.projectProgress == 6) {
    Modal.confirm({
      title: '提示',
      content: '项目运行中是否进行配置?',
      onOk() {
        recordwhere.value = {
          projectId: record.id,
          projectProgress: record.projectProgress,
        }
        configMain.value = true
        configMaintitle.value = record.projectName + '基础信息配置'
      },
      onCancel() {},
    })
  } else {
    recordwhere.value = {
      projectId: record.id,
      projectProgress: record.projectProgress,
    }
    configMain.value = true
    configMaintitle.value = record.projectName + '基础信息配置'
  }
}
const configMaincancelload = () => {
  configMain.value = false
  childRef.value.tableLoad()
}
//
const downloadConfig = record => {
  Modal.confirm({
    title: '提示',
    content: '是否下载配置文件?',
    onOk() {
      project
        .downloadConfig({
          projectId: record.id,
        })
        .then(async res => {
          // 1. 获取文件（假设文件地址是 "/files/example.txt"）
          const response = await fetch(res.data.url)
          // 2. 转换为 Blob
          const blob = await response.blob()
          // 3. 使用 file-saver 下载
          saveAs(blob, res.data.name) // 可以自定义文件名
        })
    },
  })
}
const closeconfigMain = () => {
  configMain.value = false
  childRef.value.tableLoad()
}

provide('configMaincancelload', configMaincancelload)
</script>
<style>
.editable-cell-icon,
.editable-cell-icon-check {
  cursor: pointer;
}
.editable-cell-icon:hover,
.editable-cell-icon-check:hover {
  color: #108ee9;
}
.full-modal1 {
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(90vh);
    overflow-y: auto;
  }
  .ant-modal-body {
    flex: 1;
  }
}
</style>
