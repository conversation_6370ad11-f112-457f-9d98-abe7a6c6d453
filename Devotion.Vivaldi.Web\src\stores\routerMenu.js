import { defineStore } from 'pinia'
import sysMenu from '@/api/methods/sysMenu'
export const configStore = defineStore({
  id: 'routerMenu',
  state: () => {
    return {
      dynamicRoutes: [],
    }
  },
  actions: {
    getRoutesMenu() {
      sysMenu
        .getRoutesMenu()
        .then(res => {
          this.dynamicRoutes = res.data
          return res.data
        })
        .catch()
    },
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'routerMenu',
        storage: localStorage,
        paths: ['dynamicRoutes'],
      },
    ],
  },
})
