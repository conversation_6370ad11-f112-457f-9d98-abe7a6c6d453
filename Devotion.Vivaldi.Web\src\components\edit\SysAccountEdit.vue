<template>
  <a-drawer
    :title="editTitle"
    :width="540"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-form-item
        label="账号名称"
        name="name"
        :rules="[{ required: true, message: '账号名称不能为空' }]"
      >
        <a-input v-model:value="formInfo.name" placeholder="请输入" />
      </a-form-item>
      <a-form-item
        label="手机号"
        name="mobile"
        :rules="[{ required: true, message: '手机号不能为空' }]"
      >
        <a-input v-model:value="formInfo.mobile" placeholder="请输入" />
      </a-form-item>
      <a-form-item
        label="密码"
        name="password"
        v-if="formInfo.id == 0"
        :rules="[{ required: true, message: '密码不能为空' }]"
      >
        <a-input-password
          v-model:value="formInfo.password"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        label="角色"
        name="roleId"
        :rules="[{ required: true, message: '请选择角色' }]"
      >
        <a-select
          v-model:value="formInfo.roleId"
          placeholder="请选择"
          :options="roleList"
        >
        </a-select>
      </a-form-item>

      <a-form-item
        v-if="formInfo.roleId == 4"
        label="经销商"
        name="roleId"
        :rules="[{ required: true, message: '请选择经销商' }]"
      >
        <a-select
          v-model:value="formInfo.dealerId"
          placeholder="请选择"
          :options="dealerList"
        >
        </a-select>
      </a-form-item>
      <a-form-item v-if="formInfo.roleId == 4" label="配置权限" name="isConfig">
        <a-switch v-model:checked="formInfo.isConfig" />
      </a-form-item>
      <a-form-item label="是否启用" name="isEnable">
        <a-switch v-model:checked="formInfo.isEnable" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formInfo.remark" />
      </a-form-item>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import sysRole from '@/api/methods/sysRole'
import sysAccount from '@/api/methods/sysAccount'
import sysDealer from '@/api/methods/sysDealer'
const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
})
const roleList = ref([])
const dealerList = ref([])

//表单字段的默认值
const defaultformInfo = {
  id: 0,
  name: '',
  mobile: '',
  password: '',
  roleId: null,
  isEnable: true,
  dealerId: null,
  isConfig: true,
  remark: '',
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editTitle = ref('新增')
const formRef = ref(null)

const init = id => {
  sysRole.getList().then(res => {
    roleList.value = res.data.map(x => ({
      label: x.name,
      value: x.id,
    }))
  })
  sysDealer.getList().then(res => {
    dealerList.value = res.data.map(x => ({
      label: x.dealerName,
      value: x.id,
    }))
  })

  if (id) {
    editTitle.value = '修改'
    sysAccount.get({ id }).then(res => {
      Object.assign(formInfo, res.data)
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
  }
}

const onSave = () => {
  formRef.value.validate().then(() => {
    if (formInfo.id == 0) {
      formInfo.dealerId = formInfo.dealerId == null ? 0 : formInfo.dealerId
      formInfo.isConfig = formInfo.dealerId == 0 ? true : formInfo.isConfig
      sysAccount.add(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    } else {
      sysAccount.update(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    }
  })
}
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close', 'updateData'])
const onClose = () => {
  emit('close')
}
// 使用 defineExpose 暴露方法
defineExpose({
  init,
})
</script>
