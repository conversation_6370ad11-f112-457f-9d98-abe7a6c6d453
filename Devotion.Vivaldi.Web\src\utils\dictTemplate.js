import { dictStore } from '@/stores/dict'
const dict = dictStore()
const dictTemplate = {
  tabletempInt(type, value) {
    var data = dict.getDictItems(type)
    if (data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].value == value) {
          return `<span class="${data[i].colorClass}">${data[i].label}</span>`
        }
      }
    }
  },
  tabletempbool(type, value) {
    var data = dict.getDictItems(type)
    var val = value ? 1 : 0
    if (data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].value == val) {
          return `<span class="${data[i].colorClass}">${data[i].label}</span>`
        }
      }
    }
  },
}
export default dictTemplate
