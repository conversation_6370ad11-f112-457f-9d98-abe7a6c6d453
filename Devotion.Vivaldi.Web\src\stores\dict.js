import { defineStore } from 'pinia'
import sysDict from '@/api/methods/sysDict'
export const dictStore = defineStore({
  id: 'dict',
  state: () => ({
    items: {},
  }),
  actions: {
    initDictList() {
      sysDict
        .getDictList()
        .then(res => {
          res.data.forEach(item => {
            this.items[item.type] = item.sysDictItem
          })
        })
        .catch()
    },
    getDictItems(type) {
      return this.items[type]
    },
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'dict',
        storage: localStorage,
        paths: ['items'],
      },
    ],
  },
})
