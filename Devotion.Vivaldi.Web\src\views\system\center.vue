<template>
  <div class="cent">
    <div class="centered-div">
      <a-form
        ref="formRef"
        :model="formState"
        name="basic"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        @finish="onFinish"
      >
        <a-form-item
          label="新密码"
          name="password"
          :rules="[{ required: true, message: '不能为空' }]"
        >
          <a-input-password v-model:value="formState.password" />
        </a-form-item>

        <a-form-item
          label="确认密码"
          name="repassword"
          :rules="[{ required: true, message: '不能为空' }]"
        >
          <a-input-password v-model:value="formState.repassword" />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
          <a-button type="primary" html-type="submit">保存</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, inject } from 'vue'
import sysAccount from '@/api/methods/sysAccount'
import { message } from 'ant-design-vue'
const userInfo = inject('userInfo')
const formState = reactive({
  password: '',
  repassword: '',
})
const formRef = ref(null)
const onFinish = values => {
  formRef.value.validate().then(() => {
    if (formState.password != formState.repassword) {
      message.error('两次密码不一致')
    }
    // formState.password = formInfo.dealerId == null ? 0 : formInfo.dealerId
    //  formState.repassword = formInfo.dealerId == 0 ? true : formInfo.isConfig
    var data = {
      id: userInfo.value.id,
      password: formState.password,
    }
    sysAccount.updatePassword(data).then(() => {
      message.success('成功', 1, () => {})
    })
  })
}
</script>
<style scoped>
.cent {
  margin: 0;
  height: 50vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.centered-div {
  width: auto;
  height: auto;

  text-align: center;
  padding: 20px;
}
</style>
