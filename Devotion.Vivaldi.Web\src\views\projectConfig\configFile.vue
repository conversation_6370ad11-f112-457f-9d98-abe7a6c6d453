<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="projectConfigFile"
    ref="childRef"
  >
    <template #custom-operation="{ record }">
      <a @click="download(record)">下载</a>
    </template>
  </form-table>
</template>
<script setup>
import { ref } from 'vue'
import FormTable from '../../components/FormTable.vue'
import { saveAs } from 'file-saver'
const formState = ref({
  name: { label: '名称', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
  },
  {
    title: '配置文件名称',
    dataIndex: 'configFileName',
  },
  // {
  //   title: '配置文件路径',
  //   dataIndex: 'path',
  //   ellipsis: true,
  //   width: 500,
  // },
  {
    title: '创建人',
    dataIndex: 'accountName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)
const download = async record => {
  // 1. 获取文件（假设文件地址是 "/files/example.txt"）
  const response = await fetch(record.path)
  // 2. 转换为 Blob
  const blob = await response.blob()
  // 3. 使用 file-saver 下载
  saveAs(blob, record.configFileName) // 可以自定义文件名
}
</script>

<style></style>
