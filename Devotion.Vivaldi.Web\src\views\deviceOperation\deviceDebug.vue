<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="DeviceOperation"
    pageAction="ProjectPage"
    :rowSelect="false"
    ref="childRef"
  >
    <template #custom-projectAddress="{ record }">
      {{
        record.province + record.city + record.district + record.detailedAddress
      }}
    </template>
    <template #custom-online="{ record }">
      <span v-if="record.online">
        <a-badge status="processing" color="#52c41a" />
        <span class="text-color-success">在线</span>
      </span>
      <span v-else>
        <a-badge status="error" />
        <span class="text-color-error">离线</span>
      </span>
    </template>
    <template #custom-operation="{ record }">
      <a @click="openTest(record)">在线调试</a>
    </template>
  </form-table>

 <a-modal
    v-model:open="open"
    :title="`在线调试 - ${currentProjectName}`"
    width="90%"
    wrap-class-name="full-modal1"
    style="top: 5%"
    :destroyOnClose="true"
    @cancel="stopDebugTimer"
  >
    <div>
        <div class="blockquote">
          传感器
          <!-- <a-button
            type="link"
            size="small"
            @click="refreshDeviceData"
            :loading="loading"
            style="float: right; margin-top: -2px;"
          >
            刷新数据
          </a-button> -->
        </div>
        <a-table
          :dataSource="sensorData"
          :columns="sensorColumns"
          :pagination="false"
          size="small"
          bordered
          :loading="loading"
        >
          <template #bodyCell="{ column, text }">
            <template v-if="column.dataIndex.startsWith('t') || column.dataIndex.startsWith('h')">
              <span :class="getValueClass(text)">{{ text }}°C</span>
            </template>
          </template>
          <template #emptyText>
            <span>{{ loading ? '加载中...' : '暂无数据' }}</span>
          </template>
        </a-table>

        <div class="blockquote margin-top-20">继电器</div>
        <div class="relay-container">
          <div class="relay-section">
            <div class="section-title">继电器状态 (80端口) - 显示格式: 编号 [操作值/反馈值]</div>
            <div class="relay-grid">
              <div
                v-for="i in 80"
                :key="`relay-${i}`"
                class="relay-item"
              >
                <div class="relay-number">{{ i }}</div>
                <div class="relay-values">
                  <div class="relay-operation" :class="getRelayOperationClass(i)">
                    {{ getRelayOperationStatus(i) }}
                  </div>
                  <div class="relay-separator">/</div>
                  <div class="relay-feedback" :class="getRelayFeedbackClass(i)">
                    {{ getRelayFeedbackStatus(i) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="relay-control">
            <div class="control-title">继电器控制</div>
            <div class="control-form">
              <div class="form-item">
                <div class="form-label">端口号:</div>
                <a-input-number
                  v-model:value="relayControl.port"
                  :min="1"
                  :max="80"
                  style="width: 100px"
                />
              </div>
              <div class="form-item">
                <div class="form-label">状态:</div>
                <a-radio-group v-model:value="relayControl.status">
                  <a-radio value="on">开启</a-radio>
                  <a-radio value="off">关闭</a-radio>
                </a-radio-group>
              </div>
              <a-button
                type="primary"
                :disabled="!canSendRelayCommand()"
                @click="sendRelayCommand()"
              >
                发送
              </a-button>
            </div>
          </div>
        </div>

        <div class="blockquote margin-top-20">PT1000</div>
        <div class="pt1000-container">
          <div class="pt1000-section">
            <div class="section-title">供水温度 (16个)</div>
            <div class="pt1000-grid">
              <div
                v-for="i in 16"
                :key="`supply-${i}`"
                class="pt1000-item"
              >
                <div class="pt1000-number">{{ i }}</div>
                <div class="pt1000-value" :class="getPT1000ValueClass(i, 'supply')">{{ getPT1000Value(i, 'supply') }}°C</div>
              </div>
            </div>
          </div>

          <div class="pt1000-section">
            <div class="section-title">回水温度 (16个)</div>
            <div class="pt1000-grid">
              <div
                v-for="i in 16"
                :key="`return-${i}`"
                class="pt1000-item"
              >
                <div class="pt1000-number">{{ i }}</div>
                <div class="pt1000-value" :class="getPT1000ValueClass(i, 'return')">{{ getPT1000Value(i, 'return') }}°C</div>
              </div>
            </div>
          </div>
        </div>

        <div class="blockquote margin-top-20">混水阀</div>
        <div class="mixing-valve-container">
          <div class="mixing-valve-section">
            <div class="section-title">操作值 (16端口)</div>
            <div class="mixing-valve-grid">
              <div
                v-for="i in 16"
                :key="`operation-${i}`"
                class="mixing-valve-item"
              >
                <div class="mixing-valve-number">{{ i }}</div>
                <div class="mixing-valve-value" :class="getMixingValveOperationClass(i)">{{ getMixingValveOperationValue(i) }}</div>
              </div>
            </div>
          </div>

          <div class="mixing-valve-section">
            <div class="section-title">实际反馈值 (16端口)</div>
            <div class="mixing-valve-grid">
              <div
                v-for="i in 16"
                :key="`feedback-${i}`"
                class="mixing-valve-item feedback"
              >
                <div class="mixing-valve-number">{{ i }}</div>
                <div class="mixing-valve-value" :class="getMixingValveFeedbackClass(i)">{{ getMixingValveFeedbackValue(i) }}</div>
              </div>
            </div>
          </div>

          <div class="mixing-valve-control">
            <div class="control-title">混水阀控制</div>
            <div class="control-form">
              <div class="form-item">
                <div class="form-label">端口号:</div>
                <a-input-number
                  v-model:value="mixingValveControl.port"
                  :min="1"
                  :max="16"
                  style="width: 100px"
                />
              </div>
              <div class="form-item">
                <div class="form-label">设定值(V):</div>
                <a-input-number
                  v-model:value="mixingValveControl.value"
                  :min="0"
                  :max="10"
                  :step="1"
                  :precision="0"
                  style="width: 100px"
                />
              </div>
              <a-button
                type="primary"
                :disabled="!canSendMixingValveCommand()"
                @click="sendMixingValveCommand()"
              >
                发送
              </a-button>
            </div>
          </div>
        </div>
    </div>



    <template #footer>
      <!-- <a-button key="submit" type="primary" @click="settingMainOpen = false"
        >关闭</a-button
      > -->
    </template>
  </a-modal>


</template>
<script setup>
import { ref } from 'vue'
import FormTable from '../../components/FormTable.vue'
import deviceOperation from '@/api/methods/deviceOperation'
import { message } from 'ant-design-vue'

const formState = ref({
  projectName: { label: '项目名称', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '网络状态',
    key: 'online',
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
  },
  {
    title: '唯一标识UID',
    dataIndex: 'uid',
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)

// 弹窗相关
const open = ref(false)
const currentProjectId = ref(null)
const currentProjectName = ref('') // 当前项目名称
const debugTimer = ref(null) // 调试定时器

// 传感器表格列定义
const sensorColumns = [
  {
    title: '端口号',
    dataIndex: 'port',
    key: 'port',
    width: 80,
    align: 'center'
  },
  {
    title: 'h1',
    dataIndex: 'h1',
    key: 'h1',
    width: 100,
    align: 'center'
  },
  {
    title: 'h2',
    dataIndex: 'h2',
    key: 'h2',
    width: 100,
    align: 'center'
  },
  {
    title: 't1',
    dataIndex: 't1',
    key: 't1',
    width: 100,
    align: 'center'
  },
  {
    title: 't2',
    dataIndex: 't2',
    key: 't2',
    width: 100,
    align: 'center'
  },
  {
    title: 't3',
    dataIndex: 't3',
    key: 't3',
    width: 100,
    align: 'center'
  }
]

// 传感器数据
const sensorData = ref([])
const loading = ref(false)

// 获取设备调试数据
const fetchDeviceDebugData = async (projectId) => {
  if (!projectId) {
    message.error('项目ID不能为空')
    return
  }
  loading.value = true
  try {
    const response = await deviceOperation.getDeviceDebugInfo({ projectId })
    if (response && response.code === 200 && response.data) {
      // 处理传感器数据
      if (response.data.redisTestSensorlist) {
        sensorData.value = response.data.redisTestSensorlist
      } else {
        sensorData.value = []
      }

      // 处理继电器数据
      if (response.data.redisTestRelaylist) {
        relayData.value = response.data.redisTestRelaylist
      } else {
        relayData.value = []
      }

      // 处理PT1000数据
      if (response.data.redisTestPT1000list) {
        pt1000Data.value = response.data.redisTestPT1000list
      } else {
        pt1000Data.value = []
      }

      // 处理混水阀数据
      if (response.data.redisTestMixingValvelist) {
        mixingValveData.value = response.data.redisTestMixingValvelist
      } else {
        mixingValveData.value = []
      }
    } else {
      message.error(response?.message || '获取设备调试数据失败')
      sensorData.value = []
      relayData.value = []
      pt1000Data.value = []
      mixingValveData.value = []
    }
  } catch (error) {
    console.error('获取设备调试数据出错:', error)
    message.error('获取设备调试数据出错')
    // 出错时清空数据
    sensorData.value = []
    relayData.value = []
    pt1000Data.value = []
    mixingValveData.value = []
  } finally {
    loading.value = false
  }
}


// 根据值获取样式类
const getValueClass = (value) => {
  if (typeof value !== 'number') return ''

  if (value > 50) return 'value-high'
  if (value < 20) return 'value-low'
  return 'value-normal'
}

// 继电器数据
const relayData = ref([])

// 继电器控制表单
const relayControl = ref({
  port: 1,
  status: 'off'
})

// 获取继电器项
const getRelayItem = (index) => {
  return relayData.value.find(item => item.port === index) || { port: index, getStatus: null, setStatus: false }
}

// 获取继电器操作状态文本（getStatus - 实际状态）
const getRelayOperationStatus = (index) => {
  const item = getRelayItem(index)
  // 如果 setStatus 为 null，则显示（）
  if (item.setStatus === null || item.setStatus === undefined) {
    return '（）'
  }
  // 1表示开启，0表示关闭
  return item.setStatus === "1" || item.setStatus === true ? '开启' : '关闭'
}

// 获取继电器操作状态样式类
const getRelayOperationClass = (index) => {
  const item = getRelayItem(index)
  // 如果 setStatus 为 null 或 undefined，则使用灰色样式
  if (item.setStatus === null || item.setStatus === undefined) return 'status-unknown'
  // 1表示开启，0表示关闭
  return item.setStatus === "1"  || item.setStatus === true ? 'status-on' : 'status-off'
}

// 获取继电器反馈状态文本（getStatus - 实际状态）
const getRelayFeedbackStatus = (index) => {
  const item = getRelayItem(index)
  // 如果 getStatus 为 null 或 undefined，则显示（）
  if (item.getStatus === null || item.getStatus === undefined) {
    return '（）'
  }
  // 1表示开启，0表示关闭
  return item.getStatus === 1 || item.getStatus === true ? '开启' : '关闭'
}

// 获取继电器反馈状态样式类
const getRelayFeedbackClass = (index) => {
  const item = getRelayItem(index)
  // 如果 getStatus 为 null 或 undefined，则使用灰色样式
  if (item.getStatus === null || item.getStatus === undefined) return 'status-unknown'
  // 1表示开启，0表示关闭
  return item.getStatus === 1 || item.getStatus === true ? 'status-on' : 'status-off'
}

// 检查是否可以发送继电器命令
const canSendRelayCommand = () => {
  return relayControl.value.port >= 1 &&
         relayControl.value.port <= 80 &&
         (relayControl.value.status === 'on' || relayControl.value.status === 'off')
}

// 发送继电器命令
const sendRelayCommand = () => {
  const port = relayControl.value.port
  const status = relayControl.value.status === 'on'

  // 显示加载状态
  loading.value = true

  // 调用 RelayKz 接口
  deviceOperation.RelayKz({
    projectId: currentProjectId.value,
    Port: port,
    status: status
  })
    .then(response => {
      if (response && response.code === 200) {
        message.success('继电器状态设置成功')

        // 查找继电器项
        const relayIndex = relayData.value.findIndex(item =>
          item.port === port ||
          item.port === port.toString() ||
          parseInt(item.port) === port
        )

        if (relayIndex >= 0) {
          // 更新现有项
          relayData.value[relayIndex].setStatus = status
        } else {
          // 添加新项
          relayData.value.push({
            port,
            setStatus: status,
            getStatus: null
          })
        }

        // 刷新数据
        setTimeout(() => {
          fetchDeviceDebugData(currentProjectId.value)
        }, 1000)
      } else {
        message.error(response?.message || '继电器状态设置失败')
      }
    })
    .catch(error => {
      console.error('设置继电器状态出错:', error)
      message.error('设置继电器状态出错')
    })
    .finally(() => {
      loading.value = false
    })

  console.log(`发送继电器命令：端口 ${port}，状态 ${status ? '开启' : '关闭'}`)
}

// PT1000温度数据
const pt1000Data = ref([])

// 获取PT1000温度值
const getPT1000Value = (index, type) => {
  // 尝试不同的方式查找项
  let item = pt1000Data.value.find(item => item.port === index)
  if (!item) {
    item = pt1000Data.value.find(item => item.port === index.toString())
  }
  if (!item) {
    item = pt1000Data.value.find(item => parseInt(item.port) === index)
  }

  if (!item) {
    return '无数据'
  }

  if (type === 'supply') {
    return item.waterSupplyTemperature === null || item.waterSupplyTemperature === undefined ?
      '无数据' : item.waterSupplyTemperature
  } else if (type === 'return') {
    return item.returnWaterTemperature === null || item.returnWaterTemperature === undefined ?
      '无数据' : item.returnWaterTemperature
  }

  return '无数据'
}

// 获取PT1000温度值样式类
const getPT1000ValueClass = (index, type) => {
  // 尝试不同的方式查找项
  let item = pt1000Data.value.find(item => item.port === index)
  if (!item) {
    item = pt1000Data.value.find(item => item.port === index.toString())
  }
  if (!item) {
    item = pt1000Data.value.find(item => parseInt(item.port) === index)
  }

  if (!item) return 'status-unknown'

  let value = null
  if (type === 'supply') {
    value = item.waterSupplyTemperature
  } else if (type === 'return') {
    value = item.returnWaterTemperature
  }

  if (value === null || value === undefined) return 'status-unknown'

  // 使用与传感器相同的样式逻辑
  if (value > 50) return 'value-high'
  if (value < 20) return 'value-low'
  return 'value-normal'
}

// 混水阀数据
const mixingValveData = ref([])

// 混水阀控制表单
const mixingValveControl = ref({
  port: 1,
  value: 5  // 使用整数值
})

// 获取混水阀项
const getMixingValveItem = (index) => {
  // 打印混水阀数据，帮助调试
  console.log('混水阀数据 (getMixingValveItem):', mixingValveData.value)
  console.log('查找的索引:', index)

  // 尝试不同的方式查找项
  let item = mixingValveData.value.find(item => item.port === index)
  if (!item) {
    item = mixingValveData.value.find(item => item.port === index.toString())
  }
  if (!item) {
    item = mixingValveData.value.find(item => parseInt(item.port) === index)
  }

  // 如果仍然找不到，尝试其他可能的字段名
  if (!item) {
    item = mixingValveData.value.find(item => item.Port === index)
  }
  if (!item) {
    item = mixingValveData.value.find(item => item.Port === index.toString())
  }
  if (!item) {
    item = mixingValveData.value.find(item => parseInt(item.Port) === index)
  }

  if (item) {
    console.log('找到的混水阀项:', item)
  } else {
    console.log('未找到混水阀项，使用默认值')
  }

  return item || { port: index, setValue: null, getValue: null }
}

// 获取混水阀操作值
const getMixingValveOperationValue = (index) => {
  const item = getMixingValveItem(index)

  // 尝试不同的字段名，优先使用 setValue
  let value = item.setValue
  if (value === null || value === undefined) {
    value = item.setvalue
  }
  if (value === null || value === undefined) {
    value = item.SetValue
  }

  return value === null || value === undefined ? '（）' : value + 'V'
}

// 获取混水阀反馈值
const getMixingValveFeedbackValue = (index) => {
  const item = getMixingValveItem(index)

  // 尝试不同的字段名，优先使用 getValue
  let value = item.getValue
  if (value === null || value === undefined) {
    value = item.getvalue
  }
  if (value === null || value === undefined) {
    value = item.GetValue
  }

  return value === null || value === undefined ? '（）' : value + 'V'
}

// 获取混水阀操作值样式类
const getMixingValveOperationClass = (index) => {
  const item = getMixingValveItem(index)

  // 尝试不同的字段名
  let value = item.setValue
  if (value === null || value === undefined) {
    value = item.setvalue
  }
  if (value === null || value === undefined) {
    value = item.SetValue
  }

  if (value === null || value === undefined) return 'status-unknown'
  return 'value-normal'
}

// 获取混水阀反馈值样式类
const getMixingValveFeedbackClass = (index) => {
  const item = getMixingValveItem(index)

  // 尝试不同的字段名
  let value = item.getValue
  if (value === null || value === undefined) {
    value = item.getvalue
  }
  if (value === null || value === undefined) {
    value = item.GetValue
  }

  if (value === null || value === undefined) return 'status-unknown'
  return 'value-normal'
}

// 检查是否可以发送混水阀命令
const canSendMixingValveCommand = () => {
  return mixingValveControl.value.port >= 1 &&
         mixingValveControl.value.port <= 16 &&
         mixingValveControl.value.value >= 0 &&
         mixingValveControl.value.value <= 10
}

// 发送混水阀命令
const sendMixingValveCommand = () => {
  const port = mixingValveControl.value.port
  const value = Math.round(mixingValveControl.value.value) // 确保值是整数

  // 显示加载状态
  loading.value = true

  // 调用 ReMixingValveKz 接口
  deviceOperation.ReMixingValveKz({
    projectId: currentProjectId.value,
    port: port,
    value: value
  })
    .then(response => {
      if (response && response.code === 200) {
        message.success('混水阀状态设置成功')

        // 查找混水阀项
        const valveIndex = mixingValveData.value.findIndex(item =>
          item.port === port ||
          item.port === port.toString() ||
          parseInt(item.port) === port
        )

        if (valveIndex >= 0) {
          // 更新现有项
          const item = mixingValveData.value[valveIndex]

          // 根据现有字段名更新值
          if ('setValue' in item) {
            item.setValue = value
          } else if ('setvalue' in item) {
            item.setvalue = value
          } else if ('SetValue' in item) {
            item.SetValue = value
          } else {
            // 如果没有找到匹配的字段，添加 setValue 字段
            item.setValue = value
          }
        } else {
          // 添加新项
          mixingValveData.value.push({
            port,
            setValue: value,
            getValue: null
          })
        }

        // 刷新数据
        setTimeout(() => {
          fetchDeviceDebugData(currentProjectId.value)
        }, 1000)
      } else {
        message.error(response?.message || '混水阀状态设置失败')
      }
    })
    .catch(error => {
      console.error('设置混水阀状态出错:', error)
      message.error('设置混水阀状态出错')
    })
    .finally(() => {
      loading.value = false
    })

  console.log(`发送混水阀命令：端口 ${port}，值 ${value}V`)
}

// 刷新设备调试数据
const refreshDeviceData = () => {
  if (currentProjectId.value) {
    fetchDeviceDebugData(currentProjectId.value)
  } else {
    message.warning('无法获取项目ID，无法加载数据')
  }
}

// 发送 SendHex 请求
const sendHexRequest = () => {
  if (currentProjectId.value) {
    deviceOperation.Sendhex({ projectId: currentProjectId.value })
      .then(() => {
        // 请求成功，无需处理
      })
      .catch(error => {
        console.error('SendHex 请求出错:', error)
      })
  }
}

// 启动调试定时器
const startDebugTimer = () => {
  // 先清除可能存在的定时器
  stopDebugTimer()
  // 立即执行一次
  sendHexRequest()
  // 设置定时器，每5秒执行一次
  debugTimer.value = setInterval(() => {
    sendHexRequest()
    refreshDeviceData()
  }, 5000)

  console.log('调试定时器已启动，每5秒执行一次 SendHex 请求')
}

// 停止调试定时器
const stopDebugTimer = () => {
  if (debugTimer.value) {
    clearInterval(debugTimer.value)
    debugTimer.value = null
    console.log('调试定时器已停止')

    // 在关闭窗口后执行设备初始化
    if (currentProjectId.value) {
      deviceOperation.Initialization({ projectId: currentProjectId.value })
        .then(response => {
          if (response && response.code === 200) {
            console.log('设备初始化成功')
          } else {
            console.warn('设备初始化失败:', response?.message)
          }
        })
        .catch(error => {
          console.error('设备初始化出错:', error)
        })
    }
  }
}

const openTest = (record) => {
  // 保存当前项目ID和名称
  if (record) {
    if (record.id) {
      currentProjectId.value = record.id
    }
    if (record.projectName) {
      currentProjectName.value = record.projectName
    } else {
      currentProjectName.value = '未知项目'
    }
  }

  // 初始化设备已移至关闭窗口后执行

  // 打开弹窗
  open.value = true

  // 获取设备调试数据
  if (currentProjectId.value) {
    fetchDeviceDebugData(currentProjectId.value)

    // 启动调试定时器
    startDebugTimer()
  } else {
    message.warning('无法获取项目ID，无法加载数据')
  }
}

</script>
<style scoped>
.blockquote {
  padding: 10px 15px;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
  border-left: 4px solid #1677ff;
  background-color: #f5f5f5;
}

.margin-top-20 {
  margin-top: 20px;
}

/* 传感器数据值样式 */
.value-normal {
  color: #52c41a;
}

.value-high {
  color: #ff4d4f;
  font-weight: bold;
}

.value-low {
  color: #1677ff;
  font-weight: bold;
}

/* 继电器样式 */
.relay-container {
  margin-bottom: 20px;
}

.relay-section {
  margin-bottom: 20px;
}

.relay-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

.relay-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.relay-item.feedback {
  cursor: default;
  border-color: #d9d9d9;
  background-color: #fafafa;
}

.relay-item.feedback:hover {
  border-color: #d9d9d9;
  box-shadow: none;
}

.relay-number {
  font-weight: bold;
  margin-bottom: 5px;
}

.relay-values {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.relay-operation, .relay-feedback {
  font-size: 12px;
}

.relay-separator {
  margin: 0 2px;
  color: #8c8c8c;
}

.status-on {
  color: #52c41a;
  font-weight: 500;
}

.status-off {
  color: #8c8c8c;
}

.status-unknown {
  color: #d9d9d9;
  font-style: italic;
}

.relay-control {
  margin-top: 30px;
  padding: 15px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

/* PT1000样式 */
.pt1000-container {
  margin-bottom: 20px;
}

.pt1000-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #1677ff;
}

.pt1000-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 10px;
}

.pt1000-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.pt1000-number {
  font-weight: bold;
  margin-bottom: 5px;
}

.pt1000-value {
  color: #1677ff;
  font-weight: 500;
}

.pt1000-value.status-unknown {
  color: #d9d9d9;
  font-style: italic;
}

/* 混水阀样式 */
.mixing-valve-container {
  margin-bottom: 20px;
}

.mixing-valve-section {
  margin-bottom: 20px;
}

.mixing-valve-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 10px;
}

.mixing-valve-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.mixing-valve-item.feedback {
  cursor: default;
  border-color: #d9d9d9;
  background-color: #fafafa;
}

.mixing-valve-item.feedback:hover {
  border-color: #d9d9d9;
  box-shadow: none;
}

.mixing-valve-number {
  font-weight: bold;
  margin-bottom: 5px;
}

.mixing-valve-value {
  color: #fa8c16;
  font-weight: 500;
}

.mixing-valve-value.status-unknown {
  color: #d9d9d9;
  font-style: italic;
}

.mixing-valve-control {
  margin-top: 30px;
  padding: 15px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.control-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 15px;
  color: #fa8c16;
}

.control-form {
  display: flex;
  align-items: center;
  gap: 20px;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-label {
  font-weight: 500;
  white-space: nowrap;
}
</style>
