<template>
  <!-- 详情 -->
  <a-drawer
    :title="editTitle"
    width="40%"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-form-item
        label="加湿器名称"
        name="humidifierName"
        :rules="[{ required: true, message: '加湿器名称不能为空' }]"
      >
        <a-input v-model:value="formInfo.humidifierName" placeholder="请输入" />
      </a-form-item>
      <a-form-item
        label="加湿器DO"
        name="humidifierDO"
        :rules="[{ required: true, message: '加湿器DO不能为空' }]"
      >
        <a-input-number
          :min="0"
          :max="32"
          :precision="0"
          v-model:value="formInfo.humidifierDO"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="隶属新风机" name="fafOptions">
        <a-radio-group
          name="checkboxgroup"
          :options="formInfo.fafOptions"
          v-model:value="formInfo.freshAirFanId"
          @change="fafChange"
        />
      </a-form-item>
      <a-form-item label="选择标识房间" name="roomlist">
        <a-radio-group
          v-model:value="formInfo.sensorConfigId"
          name="radioGroup"
          :options="formInfo.roomOptions"
        >
        </a-radio-group>
      </a-form-item>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup>
import { defineProps, defineEmits, ref } from 'vue'
import { message } from 'ant-design-vue'
import freshAirFan from '@/api/methods/freshAirFan'
import humidifier from '@/api/methods/humidifier'
const props = defineProps({
  editTitle: {
    type: String,
    default: '新增',
  },
  open: {
    type: Boolean,
    required: true,
  },
  formInfo: {
    type: Object,
    required: true,
  },
})
const formRef = ref(null)
const fafChange = e => {
  humidifier
    .getSensorConfigByfafid({ freshAirFanId: e.target.value })
    .then(res => {
      props.formInfo.roomOptions = res.data
    })
}
const onSave = () => {
  //提交数据
  formRef.value.validate().then(() => {
    if (props.formInfo.freshAirFanId === '') {
      message.error('请选择隶属新风机', 1)
      return
    }
    if (props.formInfo.sensorConfigId === '') {
      message.error('请添加房间', 1)
      return
    }
    if (props.formInfo.id == '') {
      humidifier
        .add(props.formInfo)
        .then(() => {
          message.success('成功', 1, () => {
            onClose()
            emit('updateData') // 触发事件
          })
        })
        .catch(error => {})
    } else {
      humidifier
        .update(props.formInfo)
        .then(() => {
          message.success('成功', 1, () => {
            onClose()
            emit('updateData') // 触发事件
          })
        })
        .catch(error => {})
    }
  })
}
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close'])
const onClose = () => {
  emit('close')
}
</script>
