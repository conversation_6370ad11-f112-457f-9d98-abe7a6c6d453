<template>
  <!--冷热源设置-->
  <a-breadcrumb style="background-color: #fff; padding: 0 0 10px 10px">
    <a-breadcrumb-item
      ><a @click="changeActive(1)">系统设置</a></a-breadcrumb-item
    >
    <a-breadcrumb-item>{{
      recordwhere.zoneType == 1
        ? '天棚区'
        : recordwhere.zoneType == 2
          ? '地板区'
          : recordwhere.zoneType == 3
            ? '风盘区'
            : ''
    }}</a-breadcrumb-item>
  </a-breadcrumb>
  <div>
    <a-card>
      <div v-if="radiationZonelist.length > 0">
        <a-row :gutter="16">
          <a-col :span="4">
            <div
              v-for="(item, index) in radiationZonelist"
              :key="item.id"
              class="listitem"
              :class="{ listitemselected: selectedIndex === index }"
              @click="handleItemClick(item, index)"
            >
              {{ item.zoneName }}
            </div>
          </a-col>
          <a-col :span="20">
            <a-descriptions
              :title="radiationZoneinfo.zoneName + '的运行信息'"
              layout="vertical"
              bordered
              :column="10"
            >
              <a-descriptions-item label="区域设置">
                <a-space wrap>
                  <a-button
                    type="primary"
                    @click="
                      () => {
                        if (!userInfo.isConfig) {
                          message.error('暂无配置权限，无法进行操作')
                          return
                        }
                        settiingshow = true
                        coolingParametersshow = false
                      }
                    "
                    >设置</a-button
                  >
                  <a-button type="primary" @click="onCoolingParameters()"
                    >运行参数</a-button
                  >
                </a-space>
              </a-descriptions-item>

              <a-descriptions-item label="运行模式">
                {{ yxmodel }}
              </a-descriptions-item>
              <a-descriptions-item label="系统状态">
                {{ systatus }}
              </a-descriptions-item>
              <a-descriptions-item label="工作模式">
                <span v-if="projectOverallSetting.operatingMode == 0"
                  >供冷</span
                >
                <span v-if="projectOverallSetting.operatingMode == 1"
                  >供暖</span
                >
                <span v-if="projectOverallSetting.operatingMode == 2"
                  >通风</span
                >
              </a-descriptions-item>
              <a-descriptions-item label="室外温度	">
                {{
                  projectOverallSetting.outdoorTemperature
                }}℃</a-descriptions-item
              >
              <a-descriptions-item label="室外湿度	">
                {{
                  projectOverallSetting.outdoorHumidity
                }}%</a-descriptions-item
              >
            </a-descriptions>
            <a-table
              class="margin-top-10"
              :columns="columns"
              :dataSource="scThermoelectricValve"
              :pagination="false"
              bordered
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'roomName'">
                    <a-tooltip>
                   <template #title>传感器编号：{{record.sensorNo}}</template>
                  {{ record.roomName }}
                  </a-tooltip>
                </template>
                <template v-if="column.key === 'setTemperature'">
                  {{ record.setTemperature }}℃
                </template>
                <template v-if="column.key === 'currentTemperature'">
                  {{ record.currentTemperature }}℃
                </template>
                <template v-if="column.key === 'setHumidity'">
                  {{ record.setHumidity }}%
                </template>
                <template v-if="column.key === 'currentHumidity'">
                  {{ record.currentHumidity }}%
                </template>
                <template v-if="column.key === 'currentDewPoint'">
                  {{ record.currentDewPoint }}
                </template>

                <template v-if="column.key === 'currentDryBulbTemperature'">
                  {{ record.currentDryBulbTemperature }}℃
                </template>

                <template v-if="column.key === 'getThermalValveStatus'">
                  <a-tooltip>
                     <template #title>热电阀DO端口：{{record.thermoelectricValveDO}}</template>
                  {{ record.getThermalValveStatus ? '开' : '关' }}
                  </a-tooltip>
                </template>
                <template v-if="column.key === 'operation'">
                  <a @click="sctvsetting(record)">设置</a>
                </template>
              </template>
            </a-table>
            <div v-if="settiingshow" class="bg-color-white padding-top-10">
              <a-card :title="radiationZoneinfo.zoneName + '设置'">
                <div class="text-font-18 padding-bottom-10">基础设定</div>
                <a-row class="margin-top-10" :gutter="16">
                  <a-col :span="12">
                    <span class="text-font-16 padding-20">工作状态</span>
                    <a-radio-group
                      v-model:value="radiationZoneValue.workingCondition"
                      name="workingConditionGroup"
                    >
                      <a-radio :value="0">关闭</a-radio>
                      <a-radio :value="1">自动</a-radio>
                    </a-radio-group>
                  </a-col>
                  <a-col :span="12">

                    <span class="text-font-16 padding-20">水泵状态(DO端口：{{radiationZoneinfo.waterPumpDO}})</span>

                    <a-radio-group
                      v-model:value="radiationZoneValue.pumpRunStatus"
                      name="pumpStatusGroup"
                    >
                      <a-radio :value="0">关闭</a-radio>
                      <a-radio :value="1">打开</a-radio>
                      <a-radio :value="2">自动</a-radio>
                    </a-radio-group>
                  </a-col>
                </a-row>
                <div style="text-align: right; margin: 10px 20px">
                  <a-button type="primary" @click="basicSettings"
                    >保存</a-button
                  >
                </div>
                <a-divider style="height: 2px; background-color: #f5f5f5" />
                <a-card
                  v-if="projectOverallSetting.operatingMode == 0"
                  title="供冷逻辑"
                >
                  <a-row>
                    <a-col :span="8">
                      <div>
                        <span class="text-font-16">热电阀</span>
                      </div>
                      <div class="margin-top-10">
                        <span class="text-font-16">开启条件：</span>
                        <a-space>
                          <span class="text-font-16">ΔTo >= </span>
                          <a-input-number
                            v-model:value="radiationZoneValue.glTopen"
                            :min="-5"
                            :step="0.1"
                            :max="5"
                          ></a-input-number>
                        </a-space>
                      </div>
                      <div class="margin-top-10">
                        <span class="text-font-16">关闭条件：</span>
                        <a-space>
                          <span class="text-font-16">ΔTo <= </span>
                          <a-input-number
                            v-model:value="radiationZoneValue.glTclose"
                            :min="-5"
                            :step="0.1"
                            precision="1"
                            :max="5"
                          ></a-input-number>
                        </a-space>
                      </div>
                    </a-col>
                    <a-col :span="8">
                      <div>
                        <span class="text-font-16">厨卫热电阀</span>
                      </div>
                      <div class="margin-top-10">
                        <span class="text-font-16">开启条件：</span>
                        <a-space>
                          <span class="text-font-16">ΔTo >= </span>
                          <a-input-number
                            v-model:value="radiationZoneValue.glCyTopen"
                            :min="-5"
                            :step="0.1"
                            :max="5"
                          ></a-input-number>
                        </a-space>
                      </div>
                      <div class="margin-top-10">
                        <span class="text-font-16">关闭条件：</span>
                        <a-space>
                          <span class="text-font-16">ΔTo <= </span>
                          <a-input-number
                            v-model:value="radiationZoneValue.glCyTclose"
                            :min="-5"
                            :step="0.1"
                            precision="1"
                            :max="5"
                          ></a-input-number>
                        </a-space>
                      </div>
                    </a-col>
                    <a-col :span="8">
                      <div>
                        <span class="text-font-16">Tc折线坐标</span>
                      </div>
                      <div class="margin-top-10">
                        <div>
                          <a-space
                            ><span class="text-font-16">A(</span>
                            <a-input-number
                              v-model:value="radiationZoneValue.glTw1"
                              :min="0"
                              :step="0.5"
                              precision="1"
                              :max="100"
                            ></a-input-number
                            ><span class="text-font-16">, </span>
                            <a-input-number
                              v-model:value="radiationZoneValue.glTc1"
                              :min="0"
                              :step="0.5"
                              precision="1"
                              :max="100"
                            ></a-input-number
                            ><span class="text-font-16">)</span>
                          </a-space>
                        </div>
                        <div class="margin-top-10">
                          <a-space
                            ><span class="text-font-16">B(</span>
                            <a-input-number
                              v-model:value="radiationZoneValue.glTw2"
                              :min="0"
                              :step="0.5"
                              precision="1"
                              :max="100"
                            ></a-input-number
                            ><span class="text-font-16">, </span>
                            <a-input-number
                              v-model:value="radiationZoneValue.glTc2"
                              :min="0"
                              :step="0.5"
                              precision="1"
                              :max="100"
                            ></a-input-number
                            ><span class="text-font-16">)</span>
                          </a-space>
                        </div>
                      </div>
                    </a-col>
                  </a-row>
                  <a-divider style="height: 2px; background-color: #f5f5f5" />
                  <div v-if="userInfo.roleId== 1||userInfo.roleId== 2">
                    <a-space>
                      <span class="text-font-16">ΔRt取值 = </span>

                      <a-input-number
                        v-model:value="radiationZoneValue.glRt"
                        :min="0"
                        :step="0.5"
                        precision="1"
                        :max="20"
                      ></a-input-number>
                    </a-space>
                  </div>
                </a-card>

                <a-card
                  v-if="projectOverallSetting.operatingMode == 1"
                  class="margin-top-20"
                  title="供暖逻辑"
                >
                  <a-row>
                    <a-col :span="8">
                      <div>
                        <span class="text-font-16">热电阀</span>
                      </div>
                      <div class="margin-top-10">
                        <span class="text-font-16">开启条件：</span>
                        <a-space>
                          <span class="text-font-16">ΔTo <= </span>
                          <a-input-number
                            v-model:value="radiationZoneValue.gnTopen"
                            :min="-5"
                            :step="0.1"
                            :max="5"
                          ></a-input-number>
                        </a-space>
                      </div>
                      <div class="margin-top-10">
                        <span class="text-font-16">关闭条件：</span>
                        <a-space>
                          <span class="text-font-16">ΔTo >= </span>
                          <a-input-number
                            v-model:value="radiationZoneValue.gnTclose"
                            :min="-5"
                            :step="0.1"
                            :max="5"
                          ></a-input-number>
                        </a-space>
                      </div>
                    </a-col>
                    <a-col :span="8">
                      <div>
                        <span class="text-font-16">厨卫热电阀</span>
                      </div>
                      <div class="margin-top-10">
                        <span class="text-font-16">开启条件：</span>
                        <a-space>
                          <span class="text-font-16">ΔTo >= </span>
                          <a-input-number
                            v-model:value="radiationZoneValue.gnCyTopen"
                            :min="-5"
                            :step="0.1"
                            :max="5"
                          ></a-input-number>
                        </a-space>
                      </div>
                      <div class="margin-top-10">
                        <span class="text-font-16">关闭条件：</span>
                        <a-space>
                          <span class="text-font-16">ΔTo <= </span>
                          <a-input-number
                            v-model:value="radiationZoneValue.gnCyTclose"
                            :min="-5"
                            :step="0.1"
                            precision="1"
                            :max="5"
                          ></a-input-number>
                        </a-space>
                      </div>
                    </a-col>
                    <a-col :span="8">
                      <div>
                        <span class="text-font-16">Tc折线坐标</span>
                      </div>
                      <div class="margin-top-10">
                        <div>
                          <a-space
                            ><span class="text-font-16">A(</span>
                            <a-input-number
                              v-model:value="radiationZoneValue.gnTw1"
                              :min="0"
                              :step="0.5"
                              precision="1"
                              :max="100"
                            ></a-input-number
                            ><span class="text-font-16">, </span>
                            <a-input-number
                              v-model:value="radiationZoneValue.gnTc1"
                              :min="0"
                              :step="0.5"
                              precision="1"
                              :max="100"
                            ></a-input-number
                            ><span class="text-font-16">)</span>
                          </a-space>
                        </div>
                        <div class="margin-top-10">
                          <a-space
                            ><span class="text-font-16">B(</span>
                            <a-input-number
                              v-model:value="radiationZoneValue.gnTw2"
                              :min="0"
                              :step="0.5"
                              precision="1"
                              :max="100"
                            ></a-input-number
                            ><span class="text-font-16">, </span>
                            <a-input-number
                              v-model:value="radiationZoneValue.gnTc2"
                              :min="0"
                              :step="0.5"
                              precision="1"
                              :max="100"
                            ></a-input-number
                            ><span class="text-font-16">)</span>
                          </a-space>
                        </div>
                      </div>
                    </a-col>
                  </a-row>
                  <a-divider style="height: 2px; background-color: #f5f5f5" />
                  <div>
                    <a-space>
                      <span class="text-font-16">ΔRt取值 = </span>

                      <a-input-number
                        v-model:value="radiationZoneValue.gnRt"
                        :min="0"
                        :step="0.5"
                        precision="1"
                        :max="20"
                      ></a-input-number>
                    </a-space>
                  </div>
                </a-card>
                <div
                  v-if="projectOverallSetting.operatingMode != 2"
                  style="text-align: right; margin: 10px 20px"
                >
                  <a-space>
                    <a-button type="primary" @click="glGnSettings"
                      >保存</a-button
                    >
                  </a-space>
                </div>
              </a-card>
              <div style="text-align: right; margin: 10px 20px">
                <a-space>
                  <a-button danger @click="settiingshow = false">关闭</a-button>
                </a-space>
              </div>
            </div>
            <div
              v-if="coolingParametersshow"
              class="bg-color-white padding-top-10"
            >
              <a-card :title="radiationZoneinfo.zoneName + '的运行参数'">
                <a-row :gutter="16">
                  <a-col :span="24">
                    <a-descriptions
                      title="逻辑计算"
                      layout=""
                      bordered
                      :labelStyle="{ width: '200px' }"
                    >
                      <a-descriptions-item label="Tc值">
                        {{ radiationZoneValue.tcValue }}℃
                      </a-descriptions-item>
                      <a-descriptions-item label="Tss计算值">
                        {{ radiationZoneValue.tssValue }}℃
                      </a-descriptions-item>
                      <a-descriptions-item label="∆R计算值" v-if="projectOverallSetting.operatingMode == 1">
                        {{


                             radiationZoneValue.gnRt

                        }}
                      </a-descriptions-item>
                      <a-descriptions-item label="Tdd计算值">
                        {{ radiationZoneValue.tddValue }}
                      </a-descriptions-item>
                      <a-descriptions-item label="水温获取方式">
                        {{ radiationZoneValue.wtaMethod == 0 ? 'Tdd' : 'Tss' }}
                      </a-descriptions-item>
                      <a-descriptions-item label="安全Te值">
                        {{ radiationZoneValue.teValue }}℃
                      </a-descriptions-item>
                      <!-- <a-descriptions-item label="水泵状态">
                        {{ radiationZoneValue.pumpStatus ? '开' : '关' }}
                      </a-descriptions-item> -->

                      <a-descriptions-item label="混水阀计算比例">
                        {{ radiationZoneValue.mixingValveRatio }}%
                      </a-descriptions-item>
                    </a-descriptions>
                    <a-divider style="height: 2px; background-color: #f5f5f5" />
                    <a-descriptions
                      title="实际执行"
                      layout=""
                      bordered
                      :labelStyle="{ width: '200px' }"
                    >
                      <a-descriptions-item label="水泵端口号">
                        {{ radiationZoneinfo.waterPumpDO }}
                      </a-descriptions-item>
                      <a-descriptions-item label="水泵状态">
                        {{ radiationZoneValue.pumpStatus ? '开' : '关' }}
                      </a-descriptions-item>
                      <a-descriptions-item label="混水阀反馈比例">
                        {{ radiationZoneValue.fkMixingValveRatio }}%
                      </a-descriptions-item>

                      <a-descriptions-item label="供水水温">
                        {{ radiationZoneValue.waterSupplyTemperature }}℃
                      </a-descriptions-item>
                      <a-descriptions-item label="回水水温">
                        {{ radiationZoneValue.returnWaterTemperature }}℃
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-col>
                </a-row>
              </a-card>
              <div style="text-align: right; margin: 10px">
                <a-space>
                  <a-button danger @click="coolingParametersshow = false"
                    >关闭</a-button
                  >
                </a-space>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
      <div v-else><a-empty /></div>
    </a-card>
  </div>
  <a-modal v-model:visible="sctvsettingshow" title="房间设置" @ok="scSettings">
    <div class="padding-tb-20">
      <a-form :model="sctvinfo" ref="formRef" layout="">
        <a-form-item label="热电阀状态" name="setThermalValveStatus">
          <a-radio-group
            v-model:value="sctvinfo.setThermalValveStatus"
            name="radioGroup"
          >
            <a-radio :value="0">关闭</a-radio>
            <!-- <a-radio :value="1">开启</a-radio> -->
            <a-radio :value="2">自动</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="设定温度" name="setTemperature">
          <a-input-number
            style="width: 120px"
            :min="0"
            :max="100"
            :step="0.5"
            addon-after="℃"
            v-model:value="sctvinfo.setTemperature"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item label="设定湿度" name="setHumidity">
          <a-input-number
            :min="0"
            :max="100"
            :step="0.5"
            style="width: 120px"
            addon-after="℃"
            v-model:value="sctvinfo.setHumidity"
            placeholder="请输入"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>
<script setup>
import {
  defineProps,
  defineEmits,
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  inject,
} from 'vue'
import radiationZone from '@/api/methods/radiationZone'
import projectOverallSettings from '@/api/methods/projectOverallSettings'
import { message } from 'ant-design-vue'
const userInfo = inject('userInfo')
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const projectId = props.recordwhere.projectId
const zoneType = props.recordwhere.zoneType
const radiationZonelist = ref([])
const settiingshow = ref(false)

const coolingParametersshow = ref(false)
const radiationZoneinfo = ref({})
const radiationZoneValue = ref({})
const projectOverallSetting = ref({})
const scThermoelectricValve = ref([])

const columns = ref([
  {
    title: '房间名称',
    key: 'roomName',
  },
  {
    title: '设定作用温度',
    key: 'setTemperature',
  },
  {
    title: '实际作用温度',
    key: 'currentTemperature',
  },
  {
    title: '设定相对湿度',
    key: 'setHumidity',
  },
  {
    title: '实际相对湿度',
    key: 'currentHumidity',
  },
  {
    title: '实际干球温度',
    key: 'currentDryBulbTemperature',
  },
  {
    title: '实际露点',
    key: 'currentDewPoint',
  },
  {
    title: '热电阀状态',
    key: 'getThermalValveStatus',
  },
  {
    title: '操作',
    key: 'operation',
    width: 100,
  },
])
//运行模式
const yxmodel = ref('')
//系统状态
const systatus = ref('')
const init = () => {
  radiationZone
    .getByProjectIdorZoneTypeList({ projectId, zoneType })
    .then(res => {
      radiationZonelist.value = res.data
      if (radiationZonelist.value.length > 0) {
        getInfo(res.data[0].id)
      }
    })
}
init()
const selectedIndex = ref(0) // 默认选中第一个项
const handleItemClick = (item, index) => {
  selectedIndex.value = index
  coolingParametersshow.value = false
  settiingshow.value = false
  getInfo(item.id)
}

const getInfo = id => {
  radiationZone
    .getRZCProjectOverallSetting({ radiationZoneId: id })
    .then(res => {
      radiationZoneinfo.value = res.data.radiationZone
      radiationZoneValue.value = res.data.radiationZoneValue
      projectOverallSetting.value = res.data.projectOverallSettings
      scThermoelectricValve.value = res.data.scThermoelectricValve
      yxmodel.value =
        res.data.projectOverallSettings.seasonMode == 0 ? '手动' : '自动'
      systatus.value =
        res.data.projectOverallSettings.workingCondition == 0
          ? '关闭'
          : res.data.projectOverallSettings.workingCondition == 1
            ? '运行'
            : res.data.projectOverallSettings.workingCondition == 2
              ? res.data.projectOverallSettings.automaticWorking == 1
                ? '值班'
                : res.data.projectOverallSettings.automaticWorking == 2
                  ? '值班'
                  : '未知'
              : '未知'
    })
}

// const getRedisProjectInfoByProjectId = () => {
//   projectOverallSettings
//     .getRedisProjectInfoByProjectId({ projectId: props.recordwhere.projectId })
//     .then(res => {
//       if (res.data != null) {
//         projectOverallSetting.value.outdoorTemperature =
//           res.data.outdoorTemperature
//         projectOverallSetting.value.outdoorHumidity = res.data.outdoorHumidity
//       }
//     })
// }
// const getRZCScThermoelectricValve = id => {
//   radiationZone
//     .getRZCScThermoelectricValve({ radiationZoneId: id })
//     .then(res => {
//       // 如果已有数据，则只更新指定字段
//       if (
//         scThermoelectricValve.value &&
//         scThermoelectricValve.value.length > 0
//       ) {
//         // 创建一个ID到新数据的映射
//         const newDataMap = {}
//         res.data.forEach(item => {
//           newDataMap[item.id] = item
//         })

//         // 更新现有数据中的指定字段
//         scThermoelectricValve.value.forEach(item => {
//           if (newDataMap[item.id]) {
//             // 只更新指定的字段
//             item.currentTemperature = newDataMap[item.id].currentTemperature
//             item.currentHumidity = newDataMap[item.id].currentHumidity
//             item.currentDewPoint = newDataMap[item.id].currentDewPoint
//             item.currentDryBulbTemperature =
//               newDataMap[item.id].currentDryBulbTemperature
//             // 热电阀状态也需要更新，因为它是显示在表格中的
//             item.getThermalValveStatus =
//               newDataMap[item.id].getThermalValveStatus
//           }
//         })
//       } else {
//         // 如果没有现有数据，则直接赋值
//         scThermoelectricValve.value = res.data
//       }
//     })
// }

///打开运行参数
const onCoolingParameters = () => {
  coolingParametersshow.value = true
  settiingshow.value = false
}

//房间设置
const sctvsettingshow = ref(false)
const sctvinfo = reactive({
  sensorConfigId: '',
  thermoelectricValveId: '',
  setTemperature: 0,
  setHumidity: 0,
  setThermalValveStatus: 0,
})
const sctvsetting = record => {
  if (!userInfo.value.isConfig) {
    message.error('暂无配置权限，无法进行操作')
    return
  }
  sctvsettingshow.value = true
  sctvinfo.sensorConfigId = record.sensorConfigId
  sctvinfo.thermoelectricValveId = record.thermoelectricValveId
  sctvinfo.setTemperature = record.setTemperature
  sctvinfo.setHumidity = record.setHumidity
  sctvinfo.setThermalValveStatus = record.setThermalValveStatus
}
//辐射区基础设定保存
const basicSettings = () => {
  var obj = radiationZoneValue.value
  var data = {
    radiationZoneValueId: obj.id,
    workingCondition: obj.workingCondition,
    pumpRunStatus: obj.pumpRunStatus,
  }
  radiationZone.basicSettings(data).then(() => {
    message.success('成功', 1, () => {
      // onClose()
      // emit('updateData') // 触发事件
    })
  })
}
//辐射区供冷供暖逻辑设定
const glGnSettings = () => {
  radiationZone.glGnSettings(radiationZoneValue.value).then(() => {
    message.success('成功', 1, () => {
      // onClose()
      // emit('updateData') // 触发事件
    })
  })
}
//房间设置
const scSettings = () => {
  radiationZone.scSettings(sctvinfo).then(() => {
    message.success('成功', 1, () => {
      sctvsettingshow.value = false
      // 使用当前选中的辐射区ID
      if (radiationZonelist.value.length > 0 && selectedIndex.value >= 0) {
        getInfo(radiationZonelist.value[selectedIndex.value].id)
      }
      // emit('updateData') // 触发事件
    })
  })
}

// 定义定时器变量
let timer = null
// 启动定时器
const startTimer = () => {
  timer = setInterval(() => {
    // getRedisProjectInfoByProjectId()
    // 如果有选中的辐射区，则刷新其数据
    if (radiationZonelist.value.length > 0 && selectedIndex.value >= 0) {
      // getRZCScThermoelectricValve(
      //   radiationZonelist.value[selectedIndex.value].id,
      // )
    }
  }, 5000)
}

// 在组件挂载后启动定时器
onMounted(() => {
  startTimer()
})

// 在组件销毁前清除定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }
})

// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close'], ['change-active'])

const changeActive = value => {
  emit('change-active', value)
}
</script>
<style scoped>
.listitem {
  padding: 15px;
  border: 1px solid #f0f0f0;
  cursor: pointer;
}
.listitemselected {
  background-color: #1677ff;
  color: white;
}
</style>
