<template>
  <a-drawer
    :title="editTitle"
    :width="540"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-form-item
        label="经销商名称"
        name="dealerName"
        :rules="[{ required: true, message: '名称不能为空' }]"
      >
        <a-input v-model:value="formInfo.dealerName" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="地址" name="address">
        <a-input v-model:value="formInfo.address" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="联系人" name="contacts">
        <a-input v-model:value="formInfo.contacts" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="联系电话" name="contactNumber">
        <a-input v-model:value="formInfo.contactNumber" placeholder="请输入" />
      </a-form-item>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import sysDealer from '@/api/methods/sysDealer'
const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
})
//表单字段的默认值
const defaultformInfo = {
  id: 0,
  dealerName: '',
  address: '',
  contacts: '',
  contactNumber: '',
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editTitle = ref('新增')
const formRef = ref(null)

const init = id => {
  if (id) {
    editTitle.value = '修改'
    sysDealer.get({ id }).then(res => {
      Object.assign(formInfo, res.data)
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
  }
}

const onSave = () => {
  formRef.value.validate().then(() => {
    if (formInfo.id == 0) {
      sysDealer.add(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    } else {
      sysDealer.update(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    }
  })
}
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close', 'updateData'])
const onClose = () => {
  emit('close')
}
// 使用 defineExpose 暴露方法
defineExpose({
  init,
})
</script>
