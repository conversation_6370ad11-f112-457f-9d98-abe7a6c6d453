import { get, post } from '@/api/request'

const sysMenu = {
  // 获取下拉框Tree选择框数据
  getTreeSelectMenu() {
    return get('sysmenu/getTreeSelectMenu')
  },
  // 获取菜单集合
  getList() {
    return get('sysmenu/list')
  },
  // 获取菜单
  getmenu() {
    return get('sysmenu/getmenu')
  },
  //获取路由菜单
  getRoutesMenu() {
    return get('sysmenu/getRoutesMenu')
  },
  //获取路由菜单
  menuTreeList(params) {
    return get('sysmenu/menuTreeList', params)
  },

  //修改是否启用
  updateIsEnabled(params) {
    return get('sysmenu/updateIsEnabled', params)
  },
  //保存
  save(params) {
    return post('sysmenu/tsave', params)
  },
  //根据id获取信息
  get(params) {
    return get('sysmenu/get', params)
  },
}
// 导出对象
export default sysMenu
