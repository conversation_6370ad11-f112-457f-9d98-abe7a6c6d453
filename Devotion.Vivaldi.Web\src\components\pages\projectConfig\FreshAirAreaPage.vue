<template>
  <div class="bg-color-white flex-row">
    <div class="flex-item-1">
      <a-tabs
        class="padding-left-20"
        v-model:activeKey="activeKey"
        @change="activeKeyChange"
      >
        <a-tab-pane key="1">
          <template #tab>
            <a-badge> 新风机</a-badge>
          </template>
        </a-tab-pane>
        <a-tab-pane key="2" tab="加湿器" force-render></a-tab-pane>
      </a-tabs>
    </div>
    <div
      class="flex-item-9 text-color-red"
      style="padding-bottom: 18px; padding-left: 20px"
    >
      （新风机最大支持32个分区）
    </div>
  </div>
  <div v-if="activeKey == '1'">
    <form-table
      :columns="columns"
      modulePath="FreshAirFan"
      method="get"
      pageAction="getList"
      :where="localRecordwhere"
      :ftableEdit="true"
      :ftableAdd="true"
      :ftableDelete="true"
      :page="false"
      @edit="fafedit"
      ref="childRef"
    >
      <template #custom-freshAirFanType="{ record }">
        <span v-if="record.freshAirFanType == 1">ivy500</span>
        <span v-else>ivy350</span>
      </template>
      <template #custom-zoneNo="{ record }"> {{ record.zoneNo }}区 </template>
    </form-table>
    <!-- 编辑 -->
    <FreshAirFanEdit
      :editTitle="fafeditTitle"
      :open="fafeditopen"
      :formInfo="fafformInfo"
      @close="fafeditopen = false"
      @updateData="refreshData"
    />
  </div>
  <div v-if="activeKey == '2'">
    <form-table
      :columns="humidifiercolumns"
      modulePath="humidifier"
      method="get"
      pageAction="getList"
      :where="localRecordwhere"
      :ftableEdit="true"
      :ftableAdd="true"
      :ftableDelete="true"
      :page="false"
      @edit="humidifieredit"
      ref="humidifierchildRef"
    >
    </form-table>
    <!-- 编辑 -->
    <HumidifierEdit
      :editTitle="humidifiereditTitle"
      :open="humidifiereditopen"
      :formInfo="humidifierformInfo"
      @close="humidifiereditopen = false"
      @updateData="humidifierrefreshData"
    />
  </div>
</template>
<script setup>
import { ref, defineProps, reactive, inject } from 'vue'
import FormTable from '../../FormTable.vue'
import sensorConfig from '@/api/methods/sensorConfig'
import FreshAirFanEdit from '../../edit/FreshAirFanEdit.vue'
import HumidifierEdit from '../../edit/HumidifierEdit.vue'
import freshAirFan from '@/api/methods/freshAirFan'
import humidifier from '@/api/methods/humidifier'
// 注入父组件的方法
const getproject = inject('getproject')
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const activeKey = ref('1')
// 创建一个响应式的本地副本
const localRecordwhere = ref({ ...props.recordwhere })
const columns = ref([
  {
    title: '分区编号',
    key: 'zoneNo',
  },
  {
    title: '新风机名称',
    dataIndex: 'freshAirFanName',
  },
  {
    title: '新风机地址',
    dataIndex: 'freshAirFanAdress',
  },
  {
    title: 'Modbus接口',
    dataIndex: 'modbus',
  },
  {
    title: '新风机类别',
    key: 'freshAirFanType',
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)
//编辑
const defaultfafformInfo = {
  id: '',
  zoneNo: null,
  projectId: localRecordwhere.value.projectId,
  freshAirFanName: '',
  freshAirFanType: null,
  freshAirFanAdress: '',
  modbus: 0,
  freshAirFanPowerSupplyDO: '0',
  supplyWaterTemperaturePort: 0,
  highTemperaturePumpId: null,
  airValveNumber: 0,
  roomOptions: [],
  fAFAirValve: [],
  zoneNoOptions: [],
}
// 使用 reactive 定义表单状态
const fafformInfo = reactive({ ...defaultfafformInfo })
const fafeditopen = ref(false)
const fafeditTitle = ref('新增')

const fafedit = record => {
  freshAirFan
    .getSensorConfigOptionsList({
      projectId: localRecordwhere.value.projectId,
    })
    .then(res => {
      fafformInfo.roomOptions = res.data
    })
  freshAirFan
    .getZoneNoList({
      projectId: localRecordwhere.value.projectId,
    })
    .then(res => {
      fafformInfo.zoneNoOptions = res.data
    })
  if (record.id) {
    fafeditTitle.value = '修改'
    freshAirFan.get({ id: record.id }).then(res => {
      Object.assign(fafformInfo, res.data.freshAirFan)
      fafformInfo.fAFAirValve = res.data.freshAirFanAirValve
      fafeditopen.value = true
    })
  } else {
    fafeditTitle.value = '新增'
    Object.assign(fafformInfo, defaultfafformInfo)
    fafformInfo.freshAirFanName = '新风机'
    fafformInfo.fAFAirValve = []
    fafeditopen.value = true
  }
}

const humidifiercolumns = ref([
  {
    title: '加湿器编号',
    dataIndex: 'humidifierNo',
    resizable: true,
    width: 150,
  },
  {
    title: '加湿器名称',
    dataIndex: 'humidifierName',
    width: 300,
  },
  {
    title: '加湿器DO',
    dataIndex: 'humidifierDO',
    width: 300,
  },
  {
    title: '隶属新风机',
    dataIndex: 'fafNames',
    width: 300,
  },
  {
    title: '选择标识房间',
    dataIndex: 'roomName',
    width: 300,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const humidifierchildRef = ref(null)
//编辑
const defaulthumidifierformInfo = {
  id: '',
  projectId: localRecordwhere.value.projectId,
  humidifierName: '',
  humidifierDO: 0,
  freshAirFanId: '',
  sensorConfigId: '',
  roomOptions: [],
  fafOptions: [],
}
// 使用 reactive 定义表单状态
const humidifierformInfo = reactive({ ...defaulthumidifierformInfo })
const humidifiereditopen = ref(false)
const humidifiereditTitle = ref('新增')

const humidifieredit = record => {
  freshAirFan
    .getOptionsList({
      projectId: localRecordwhere.value.projectId,
    })
    .then(res => {
      humidifierformInfo.fafOptions = res.data
    })
  if (record.id) {
    humidifiereditTitle.value = '修改'
    humidifier.get({ id: record.id }).then(res => {
      Object.assign(humidifierformInfo, res.data)
      humidifiereditopen.value = true
    })
  } else {
    humidifiereditTitle.value = '新增'
    Object.assign(humidifierformInfo, defaulthumidifierformInfo)
    var number = humidifierchildRef.value.getDataSource().length + 1
    humidifierformInfo.humidifierName = number + '号加湿器'
    humidifiereditopen.value = true
  }
}
const refreshData = () => {
  childRef.value.tableLoad()
  getproject()
}
const humidifierrefreshData = () => {
  humidifierchildRef.value.tableLoad()
}
</script>
<style scoped></style>
