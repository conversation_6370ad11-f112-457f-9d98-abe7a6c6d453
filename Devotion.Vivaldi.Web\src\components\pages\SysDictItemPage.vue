<template>
  <form-table
    :columns="columns"
    modulePath="sysDictItem"
    :where="recordwhere"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    @edit="edit"
    ref="childRef"
  >
    <template #custom-isEnable="{ record }">
      <a-switch
        v-model:checked="record.isEnable"
        checked-children="是"
        un-checked-children="否"
        @change="sysDictItemhandleSwitchChange(record)"
      />
    </template>
    <template #custom-label="{ record }">
      <span :class="record.colorClass">{{ record.label }}</span>
    </template>
  </form-table>
  <!-- 编辑 -->
  <SysDictItemEdit
    :editTitle="editTitle"
    :open="editopen"
    :formInfo="formInfo"
    @close="editopen = false"
    @updateData="refreshData"
  >
  </SysDictItemEdit>
</template>
<script setup>
import { ref, defineProps, reactive, toRaw, onMounted } from 'vue'
import FormTable from '../../components/FormTable.vue'
import SysDictItemEdit from '../../components/edit/SysDictItemEdit.vue'
import sysDictItem from '@/api/methods/sysDictItem'
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
  },
  {
    title: '字典标签',
    dataIndex: 'label',
    key: 'label',
  },
  {
    title: '字典值',
    dataIndex: 'value',
    key: 'value',
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
  },
  {
    title: '颜色样式',
    dataIndex: 'colorClass',
    key: 'colorClass',
  },
  {
    title: '状态',
    key: 'isEnable',
    dataIndex: 'isEnable',
  },
  {
    title: '备注',
    key: 'remark',
    dataIndex: 'remark',
  },

  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)

//修改是否启用
const sysDictItemhandleSwitchChange = record => {
  var data = {
    id: record.id,
    isEnabled: record.isEnable,
  }
  sysDictItem.updateIsEnabled(data).then(() => {
    refreshData()
  })
}
//

//编辑
const defaultformInfo = {
  id: 0,
  dictId: props.recordwhere.dictId,
  label: '',
  colorClass: 'default',
  value: 0,
  sort: 0,
  isEnable: true,
  remark: '',
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editopen = ref(false)
const editTitle = ref('新增')
const edit = record => {
  console.log(formInfo.dictId)
  // 触发自定义事件，父组件会监听这个事件
  if (record.id) {
    editTitle.value = '修改'
    sysDictItem.get({ id: record.id }).then(res => {
      Object.assign(formInfo, res.data)
      console.log(formInfo.dictId)
      editopen.value = true
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
    editopen.value = true
  }
}

const refreshData = () => {
  childRef.value.tableLoad()
}
defineExpose({
  refreshData,
})
</script>
