<template>
  <a-drawer
    :title="editTitle"
    width="50%"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            label="小区名称"
            name="communityName"
            :rules="[{ required: true, message: '小区名称不能为空' }]"
          >
            <a-input
              v-model:value="formInfo.communityName"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="房屋类型"
            name="houseType"
            :rules="[{ required: true, message: '请选择房屋类型' }]"
          >
            <a-select
              v-model:value="formInfo.houseType"
              placeholder="请选择"
              :options="houseTypedata"
            >
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            label="省市区"
            name="provincecitydistrict"
            :rules="[{ required: true, message: '请选择省市区' }]"
          >
            <a-cascader
              v-model:value="formInfo.provincecitydistrict"
              :options="alllist"
              placeholder="请选择"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="详细地址"
            name="detailedAddress"
            :rules="[{ required: true, message: '详细地址不能为空' }]"
          >
            <a-input
              v-model:value="formInfo.detailedAddress"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="联系人" name="contactPerson">
            <a-input
              v-model:value="formInfo.contactPerson"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="联系电话" name="contactPhone">
            <a-input
              v-model:value="formInfo.contactPhone"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="联系邮箱" name="contactEmail">
            <a-input
              v-model:value="formInfo.contactEmail"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row v-if="formInfo.id == 0">
        <a-col :span="8">
          <a-form-item label="项目个数">
            <a-select
              v-model:value="formInfo.projectsNumber"
              placeholder="请选择"
              :options="projectsNumberoptions"
              @change="projectsNumberChange"
            >
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <div>
        <a-card
          v-for="(item, index) in formInfo.projectsinfo"
          :key="item.id"
          :title="item.title"
          style="width: 100%; margin-bottom: 10px"
        >
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item
                label="项目名称"
                :name="['projectsinfo', index, 'projectName']"
                :rules="[
                  {
                    required: true,
                    message: '项目名称不能为空',
                  },
                ]"
              >
                <a-input
                  v-model:value="item.projectName"
                  placeholder="请输入"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                label="地址"
                :name="['projectsinfo', index, 'detailedAddress']"
                :rules="[
                  {
                    required: true,
                    message: '地址不能为空',
                  },
                ]"
              >
                <a-input
                  v-model:value="item.detailedAddress"
                  placeholder="请输入"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="总面积（单位：平米）" name="totalArea">
                <a-input-number
                  v-model:value="item.totalArea"
                  placeholder="请输入"
                  style="width: 100%"
                  :min="0"
                  :step="0.01"
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-form-item
                label="业主姓名"
                :name="['projectsinfo', index, 'ownerName']"
                :rules="[
                  {
                    required: true,
                    message: '业主姓名不能为空',
                  },
                ]"
              >
                <a-input v-model:value="item.ownerName" placeholder="请输入" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item
                label="业主电话"
                :name="['projectsinfo', index, 'ownerPhone']"
                :rules="[
                  {
                    required: true,
                    message: '业主电话不能为空',
                  },
                ]"
              >
                <a-input v-model:value="item.ownerPhone" placeholder="请输入" />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="业主邮箱" name="ownerEmail">
                <a-input v-model:value="item.ownerEmail" placeholder="请输入" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
      </div>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref, reactive } from 'vue'
import community from '@/api/methods/community'
import { dictStore } from '@/stores/dict'
import { message } from 'ant-design-vue'
import area from '@/utils/area'
const alllist = ref(area.transformAreaData())
const dict = dictStore()
var houseTypedata = dict.getDictItems('houseType')

const projectsNumberoptions = ref([
  { label: 0, value: 0 },
  { label: 1, value: 1 },
  { label: 2, value: 2 },
  { label: 3, value: 3 },
  { label: 4, value: 4 },
  { label: 5, value: 5 },
  { label: 6, value: 6 },
  { label: 7, value: 7 },
  { label: 8, value: 8 },
  { label: 9, value: 9 },
  { label: 10, value: 10 },
])

const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
})
//表单字段的默认值
const defaultformInfo = {
  id: 0,
  communityName: '',
  provincecitydistrict: null,
  province: '',
  city: '',
  district: '',
  detailedAddress: '',
  houseType: null,
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  projectsNumber: 0,
  projectsinfo: [],
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editTitle = ref('新增')
const formRef = ref(null)

const init = id => {
  if (id) {
    editTitle.value = '修改'
    community.get({ id }).then(res => {
      Object.assign(formInfo, res.data)
      formInfo.provincecitydistrict = [
        formInfo.province,
        formInfo.city,
        formInfo.district,
      ]
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
  }
}

const onSave = () => {
  formRef.value.validate().then(() => {
    if (formInfo.id == 0) {
      community.add(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    } else {
      community.update(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    }
  })
}
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close', 'updateData'])
const onClose = () => {
  emit('close')
}
const projectsNumberChange = (value, option) => {
  var projectsinfo = []
  for (let i = 1; i <= value; i++) {
    var item = {
      id: Date.now(),
      title: '项目' + i + '基本信息',
      projectName: undefined,
      detailedAddress: formInfo.detailedAddress,
      totalArea: 0,
      ownerName: '',
      ownerPhone: '',
      ownerEmail: '',
    }
    projectsinfo.push(item)
  }
  formInfo.projectsinfo = projectsinfo
}
// 使用 defineExpose 暴露方法
defineExpose({
  init,
})
</script>
