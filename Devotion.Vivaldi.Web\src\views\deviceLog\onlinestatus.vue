<template>
  <form-table
    :formState="formState"
    :columns="columns"
    :where="recordwhere"
    modulePath="DeviceMqttLog"
    pageAction="OnlineStatusPage"
    :rowSelect="false"
  >
    <template #custom-online="{ record }">
      <span v-if="record.online">上线</span>
      <span v-else>下线</span>
    </template>
  </form-table>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import FormTable from '../../components/FormTable.vue'
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})

const formState = ref({
  deviceSn: { label: '设备编码', value: '', type: 'text' },
  online: {
    label: '状态',
    value: null,
    type: 'select',
    data: [
      { value: 1, label: '上线' },
      { value: 0, label: '下线' },
    ],
  },
  createTime: { label: '日期范围', value: '', type: 'time' },
})

onMounted(() => {
  if (props.recordwhere.deviceSn) {
    formState.value.deviceSn.value = props.recordwhere.deviceSn
  }
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 60,
  },
  {
    title: '时间',
    dataIndex: 'createTime',
    width: 150,
  },
  {
    title: '产品ID',
    dataIndex: 'productId',
    width: 150,
  },
  {
    title: '设备编码',
    dataIndex: 'uid',
    width: 150,
  },
  {
    title: '上下线状态',
    key: 'online',
    width: 150,
  },
  {
    title: '内容',
    dataIndex: 'content',
    ellipsis: true,
  },
])
</script>
<style></style>
