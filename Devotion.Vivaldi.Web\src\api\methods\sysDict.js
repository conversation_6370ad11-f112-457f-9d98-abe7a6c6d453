import { post, get } from '@/api/request'

const sysDict = {
  //根据id获取信息
  get(params) {
    return get('sysDict/get', params)
  },
  //保存
  save(params) {
    return post('sysDict/save', params)
  },
  //修改是否启用
  updateIsEnabled(params) {
    return get('sysDict/updateIsEnabled', params)
  },
  //新增
  add(params) {
    return post('sysDict/add', params)
  },
  //更新
  update(params) {
    return post('sysDict/update', params)
  },
  //查询字典值
  getDictList() {
    return get('sysDict/getDictList')
  },
}
// 获取字典分页信息
export default sysDict
