import { defineStore } from 'pinia'
export const useUserStore = defineStore({
    id: 'user',
    state: () => {
      return {
        token: '',
        userInfo: {},
        permissions: [],
      }
    },
    actions: {

    },
    persist: {
      enabled: true,
      strategies: [{
        key: 'user',
        storage: localStorage,
        paths: ['token', 'userInfo', 'permissions' ],
      }]
    }
  })