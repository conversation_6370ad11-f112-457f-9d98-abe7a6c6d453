<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="LogError"
    :rowSelect="false"
    :ftableDetails="true"
    @details="details"
  >
  </form-table>
  <!-- 详情 -->
  <a-modal
    v-model:open="detailsopen"
    title="详情"
    width="60%"
    wrap-class-name="full-modal"
  >
    <a-descriptions title="" bordered :column="2">
      <a-descriptions-item label="ID">
        {{ Info.id }}
      </a-descriptions-item>
      <a-descriptions-item label="方法">
        {{ Info.actionName }}
      </a-descriptions-item>
      <a-descriptions-item label="异常码">
        {{ Info.code }}
      </a-descriptions-item>
      <a-descriptions-item label="异常内容"
        >{{ Info.message }}
      </a-descriptions-item>
      <a-descriptions-item label="异常时间">
        {{ Info.createTime }}
      </a-descriptions-item>
      <a-descriptions-item label="备注">
        {{ Info.remark }}
      </a-descriptions-item>
    </a-descriptions>
    <a-divider orientation="left" plain="true"></a-divider>
    <a-descriptions title="" :column="1" bordered>
      <a-descriptions-item label="异常完整内容">
        {{ Info.exception }}
      </a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button key="submit" type="primary" @click="detailsHandleCancel"
        >关闭</a-button
      >
    </template>
  </a-modal>
</template>
<script setup>
import { ref } from 'vue'
import FormTable from '../../components/FormTable.vue'
import logError from '@/api/methods/logError'
const formState = ref({
  actionName: { label: '方法', value: '', type: 'text' },
  code: { label: '异常码', value: '', type: 'text' },
  createTime: { label: '日期范围', value: '', type: 'time' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '方法',
    dataIndex: 'actionName',
    ellipsis: true,
  },
  {
    title: '异常码',
    dataIndex: 'code',
    width: 100,
  },
  {
    title: '异常内容',
    dataIndex: 'message',
    ellipsis: true,
  },

  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 100,
  },
])
//详情
const detailsopen = ref(false)
const Info = ref({})
const details = record => {
  logError.detail({ id: record.id }).then(res => {
    Info.value = res.data
    detailsopen.value = true
  })
}
const detailsHandleCancel = () => {
  detailsopen.value = false
}
</script>
<style></style>
