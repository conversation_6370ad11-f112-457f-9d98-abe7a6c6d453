<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="project"
    pageAction="RunPage"
    :rowSelect="false"
    ref="childRef"
  >
    <template #custom-projectAddress="{ record }">
      {{
        record.province + record.city + record.district + record.detailedAddress
      }}
    </template>
    <template #custom-online="{ record }">
      <span v-if="record.online">
        <a-badge status="processing" color="#52c41a" />
        <span class="text-color-success">在线</span>
      </span>
      <span v-else>
        <a-badge status="error" />
        <span class="text-color-error">离线</span>
      </span>
    </template>
    <template #custom-operation="{ record }">
      <a @click="upgrade(record)">升级</a>
    </template>
  </form-table>

  <a-modal
    :open="open"
    title="升级"
    width="40%"
    @cancel="onClose"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <div class="padding-tb-50">
      <div class="flex-column">
        <a-upload
          :file-list="fileList"
          :max-count="1"
          :before-upload="beforeUpload"
          @remove="handleRemove"
        >
          <a-button>
            <upload-outlined></upload-outlined>
            上传OTA文件
          </a-button>
        </a-upload>
        <div
          class="padding-tb-20"
          v-if="current == 1 || current == 3 || current == 5"
        >
          <a-spin />正在等待设备回复...
        </div>
        <div class="padding-tb-20" v-if="current == 2">
          收到68准备指令，可以执行下一步
        </div>
        <div class="padding-tb-20" v-if="current == 4">
          收到6A指令，可以执行下一步
        </div>
        <div class="padding-tb-20" v-if="current == 6">更新成功</div>
        <div class="padding-top-20" v-if="fileList.length > 0">
          <a-space>
            <a-button
              type="primary"
              v-if="current == 0 && fileList.length > 0"
              @click="pIssuance"
              >发送升级准备指令</a-button
            >
            <a-button type="primary" v-if="current == 2" @click="dDataFrames"
              >发送文件数据帧</a-button
            >
            <a-button type="primary" v-if="current == 4" @click="disDocuments"
              >发送文件进行升级</a-button
            >
          </a-space>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button key="submit" type="primary" @click="onClose">关闭</a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { ref } from 'vue'
import FormTable from '../../components/FormTable.vue'
import deviceOperation from '@/api/methods/deviceOperation'
import { Modal, message, Upload } from 'ant-design-vue'
const formState = ref({
  projectName: { label: '项目名称', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '网络状态',
    key: 'online',
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
  },
  {
    title: '系统版本',
    dataIndex: 'systemVersion',
  },

  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)
//编辑
const open = ref(false)
const fileList = ref([])
const uid = ref('')
// 定义定时器变量
let timer = null
const current = ref(0)

const refreshData = () => {
  childRef.value.tableLoad()
}
const upgrade = record => {
  uid.value = record.uid
  getUpgradeStatus()
  open.value = true
  startTimer()
}

const getUpgradeStatus = () => {
  deviceOperation.getUpgradeStatus({ uid: uid.value }).then(res => {
    current.value = parseInt(res.data)
  })
}
const onClose = () => {
  open.value = false
  current.value = 0
  fileList.value = []
  deviceOperation.setUpgradeStatus({ uid: uid.value }).then(res => {})
  clearInterval(timer)
}
// 启动定时器
const startTimer = () => {
  timer = setInterval(() => {
    getUpgradeStatus()
  }, 3000)
}
// 开始准备
const pIssuance = () => {
  deviceOperation.preparationIssuance({ uid: uid.value }).then(res => {
    getUpgradeStatus()
  })
}
// 下发文件数据帧
const dDataFrames = () => {
  const formData = new FormData()
  fileList.value.forEach(file => {
    formData.append('file', file)
  })
  formData.append('uid', uid.value)
  deviceOperation.distributeDataFrames(formData).then(res => {})
}

// 下发文件
const disDocuments = () => {
  const formData = new FormData()
  fileList.value.forEach(file => {
    formData.append('file', file)
  })
  formData.append('uid', uid.value)
  deviceOperation.distributeDocuments(formData).then(res => {})
}

const beforeUpload = file => {
  const fileName = file.name
  const fileSuffix = fileName
    .substring(fileName.lastIndexOf('.') + 1)
    .toLowerCase()
  if (fileSuffix !== 'bin') {
    message.error(`${fileName} 不是 bin 文件，请重新上传！`)
    return false
  }
  fileList.value = [...(fileList.value || []), file]
  return false
}
const handleRemove = file => {
  const index = fileList.value.indexOf(file)
  const newFileList = fileList.value.slice()
  newFileList.splice(index, 1)
  fileList.value = newFileList
}
</script>
<style scoped>
.steps-content {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  height: auto;
  text-align: center;
  padding-top: 80px;
}

.steps-action {
  margin-top: 24px;
}

[data-theme='dark'] .steps-content {
  background-color: #2f2f2f;
  border: 1px dashed #404040;
}
</style>
