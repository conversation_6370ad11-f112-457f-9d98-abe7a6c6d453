import { post, get } from '@/api/request'

const sysUser = {
  //根据id获取信息
  get(params) {
    return get('sysUser/get', params)
  },
  //新增
  add(params) {
    return post('sysUser/add', params)
  },
  //保存
  save(params) {
    return post('sysUser/save', params)
  },
  //修改是否启用
  updateIsEnabled(params) {
    return get('sysUser/updateIsEnabled', params)
  },
  //发送短信
  sendCode(params) {
    return get('sysUser/sendCode', params)
  },
}
export default sysUser
