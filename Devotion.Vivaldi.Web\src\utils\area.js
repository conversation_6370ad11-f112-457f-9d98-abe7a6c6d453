import areaData from './areaData.json'

const area = {
  // 获取所有省份
  getAllProvince() {
    return areaData.map(province => ({
      label: province.name,
      value: province.name,
      code: province.code,
    }))
  },
  // 获取所有城市
  getAllCity() {
    return areaData.flatMap(province =>
      (province.city || []).map(city => ({
        label: city.name,
        value: city.name,
        code: city.code,
      })),
    )
  },
  // 获取所有区域（区/县）
  getAllAreas() {
    return areaData.flatMap(province =>
      (province.city || []).flatMap(city =>
        (city.area || []).map(area => ({
          label: area.name,
          value: area.name,
          code: area.code,
        })),
      ),
    )
  },
  // 根据省份代码获取该省份的所有城市
  getCityByProvinceCode(provinceCode) {
    const province = this.getProvinceByCode(provinceCode)
    return province
      ? province.city.map(city => ({
          label: city.name,
          value: city.name,
          code: city.code,
        }))
      : []
  },
  // 根据城市代码获取该城市的所有区/县
  getAreaByCityCode(cityCode) {
    for (const province of areaData) {
      const city = province.city.find(city => city.code === cityCode)
      if (city) {
        return (city.area || []).map(area => ({
          label: area.name,
          value: area.name,
          code: area.code,
        }))
      }
    }
    return []
  },
  // 根据省份代码获取该省份
  getProvinceByCode(code) {
    return areaData.find(province => province.code === code)
  },
  transformAreaData() {
    return areaData.map(item => {
      const transformedItem = {
        value: item.name,
        label: item.name,
        code: item.code,
        children: item.city
          ? item.city.map(city => {
              return {
                value: city.name,
                label: city.name,
                code: city.code,
                children: city.area
                  ? city.area.map(area => {
                      return {
                        value: area.name,
                        label: area.name,
                        code: area.code,
                      }
                    })
                  : [],
              }
            })
          : [],
      }
      return transformedItem
    })
  },
}
export default area
