<template>
  <div class="bg-color-white padding-10">
    <a-descriptions title="项目基本信息" layout="vertical" bordered :column="7">
      <a-descriptions-item label="创建日期">{{
        info.createTime != null ? info.createTime.split(' ')[0] : ''
      }}</a-descriptions-item>
      <a-descriptions-item label="项目名称">{{
        info.projectName
      }}</a-descriptions-item>
       <a-descriptions-item label="网络状态">

        <span v-if="onlineinfo.online">
          <a-badge status="processing" color="#52c41a" />
          <span class="text-color-success">在线</span>
        </span>
        <span v-else>
          <a-badge status="error" />
          <span class="text-color-error">离线</span>
        </span>
      </a-descriptions-item>
      <a-descriptions-item  v-if="!onlineinfo.online" label="离线时间">

           <span class="text-color-error">{{onlineinfo.updateTime}}</span>
      </a-descriptions-item>
        <a-descriptions-item  v-if="onlineinfo.online" label="运行时间">
           <span>{{ currentTime }}</span>
      </a-descriptions-item>
      <a-descriptions-item label="建筑面积">{{
        info.totalArea
      }}</a-descriptions-item>
      <a-descriptions-item label="项目地址">
        {{
          info.province + info.city + info.district + info.detailedAddress
        }}</a-descriptions-item>
      <a-descriptions-item label="业主姓名">{{
        info.ownerName
      }}</a-descriptions-item>
      <a-descriptions-item label="业主电话">
        {{ info.ownerPhone }}
      </a-descriptions-item>
      <!-- <a-descriptions-item label="业主邮箱">
        {{ info.ownerEmail }}</a-descriptions-item> -->
      <a-descriptions-item label="经销商名称">
        {{ info.dealerName }}</a-descriptions-item>
      <a-descriptions-item label="开/竣工时间">
        {{
          (info.startTime != null ? info.startTime.split(' ')[0] : '') +
          '/' +
          (info.completionTime != null ? info.completionTime.split(' ')[0] : '')
        }}
      </a-descriptions-item>
      <a-descriptions-item label="工程经理">
        {{ info.engineeringManager }}</a-descriptions-item>
      <a-descriptions-item label="工程设计">
        {{ info.engineeringDesign }}</a-descriptions-item>
      <!-- <a-descriptions-item label="工程调试">{{
        info.engineeringDebug
      }}</a-descriptions-item> -->
      <a-descriptions-item label="工程售后">{{
        info.elngineeringAftersales
      }}</a-descriptions-item>
      <a-descriptions-item label="竣工图纸">
        <div v-if="
          info.engineeringDrawings != null && info.engineeringDrawings != ''
        " v-for="(item, index) in JSON.parse(info.engineeringDrawings)">
          <a :href="item.url">{{ item.name }}</a>
        </div>
      </a-descriptions-item>

    </a-descriptions>
  </div>
  <div class="bg-color-white padding-10">
    <a-descriptions title="项目运行信息" layout="vertical" bordered :column="10">
      <a-descriptions-item label="功能分区" :span="4">
        <a-space wrap>
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="1"> 天棚区 </a-menu-item>
                <a-menu-item key="2"> 地板区 </a-menu-item>
              </a-menu>
            </template>
            <a-button type="primary">辐射区
              <DownOutlined />
            </a-button>
          </a-dropdown>
          <a-button type="primary" @click="changeActive(3, 0)">新风区</a-button>
          <a-button type="primary" @click="changeActive(4, 0)">冷热源</a-button>
        </a-space>
      </a-descriptions-item>

      <a-descriptions-item label="运行模式">{{ yxmodel }}</a-descriptions-item>
      <a-descriptions-item label="系统状态">
        {{ systatus }}
      </a-descriptions-item>
      <a-descriptions-item label="工作模式">
        <span v-if="settingsinfo.operatingMode == 0">供冷</span>
        <span v-if="settingsinfo.operatingMode == 1">供暖</span>
        <span v-if="settingsinfo.operatingMode == 2">通风</span>
      </a-descriptions-item>
      <a-descriptions-item label="室外温度">{{ settingsinfo.outdoorTemperature }}℃
      </a-descriptions-item>
      <a-descriptions-item label="室外湿度">{{ settingsinfo.outdoorHumidity }}%
      </a-descriptions-item>
      <a-descriptions-item label="报警级别">
        <span v-if="deviceErrorlist.length > 0" class="text-color-error">
          {{ deviceErrorlist.length }}个报警
        </span>
        <span v-else class="text-color-success">正常</span>
      </a-descriptions-item>
    </a-descriptions>
  </div>

  <!-- 报警信息详情 -->
  <div v-if="deviceErrorlist.length > 0" class="bg-color-white padding-10">
    <a-descriptions title="报警信息详情" layout="vertical" bordered>
      <a-descriptions-item label="报警列表" :span="4">
        <a-list size="small" bordered>
          <a-list-item v-for="(error, index) in deviceErrorlist" :key="index">
            <a-list-item-meta>
              <template #title>
                <span class="text-color-error">{{ error.errorMsg }}</span>
              </template>
              <template #description>
                <a-space>
                  <a-tag color="orange">类型: {{ error.type }}</a-tag>
                  <a-tag color="red">错误代码: {{ error.errorCode }}</a-tag>
                  <a-tag color="blue">端口: {{ error.port }}</a-tag>
                </a-space>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </a-list>
      </a-descriptions-item>
    </a-descriptions>
  </div>

  <div style="text-align: right; margin: 10px 20px">
    <a-button type="primary" @click="settiingclick">设置</a-button>
  </div>
  <div v-if="settiingshow" class="bg-color-white padding-10">
    <a-card title="项目总体设置">
      <div class="text-font-18 padding-bottom-20">房间默认设定值</div>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="供冷">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-space>
                  <span class="text-font-16 padding-right-20">温度</span>
                  <a-input-number v-model:value="settingsinfo.setGlTemperature" :min="0" :max="100" :step="0.5"
                    style="width: 120px" addon-after="℃"></a-input-number>
                </a-space>
              </a-col>
              <a-col :span="12">
                <a-space>
                  <span class="text-font-16 padding-right-20">湿度</span>
                  <a-input-number v-model:value="settingsinfo.setGlHumidity" :min="0" :max="100" :step="0.5"
                    style="width: 120px" addon-after="%"></a-input-number>
                </a-space>
              </a-col>
            </a-row> </a-card></a-col>
        <a-col :span="12">
          <a-card title="供暖">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-space>
                  <span class="text-font-16 padding-right-20">温度</span>
                  <a-input-number v-model:value="settingsinfo.setGnTemperature" :min="0" :max="100" :step="0.5"
                    style="width: 120px" addon-after="℃"></a-input-number>
                </a-space>
              </a-col>
              <a-col :span="12">
                <a-space>
                  <span class="text-font-16 padding-right-20">湿度</span>
                  <a-input-number v-model:value="settingsinfo.setGnHumidity" :min="0" :max="100" :step="0.5"
                    style="width: 120px" addon-after="%"></a-input-number>
                </a-space>
              </a-col> </a-row></a-card></a-col>
      </a-row>

      <a-divider style="height: 2px; background-color: #f5f5f5" />
      <div class="text-font-18 padding-bottom-20">季节模式</div>
      <span class="text-font-16 padding-20">模式转换设置：</span>
      <a-radio-group v-model:value="settingsinfo.seasonMode" name="seasonModeradioGroup">
        <a-radio :value="0">手动</a-radio>
        <a-radio :value="1">自动</a-radio>
      </a-radio-group>
      <div class="padding-20" v-if="settingsinfo.seasonMode == 0">
        <span class="text-font-16 padding-right-20">手动切换：</span>
        <a-radio-group v-model:value="settingsinfo.manualSwitching" name="manualSwitchingradioGroup">
          <a-radio :value="0">供冷</a-radio>
          <a-radio :value="1">供暖</a-radio>
          <a-radio :value="2">通风</a-radio>
        </a-radio-group>
      </div>
      <div class="padding-20" v-if="settingsinfo.seasonMode == 1">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-card title="春季供暖">
              <a-row :gutter="8">
                <a-col :span="12">
                  <div class="text-font-15">开始时间</div>
                  <a-select style="width: 60px; margin-right: 10px" :options="startoptions"
                    v-model:value="automaticSeason.springStartTime1"></a-select>
                  <a-select style="width: 60px" :options="endoptions"
                    v-model:value="automaticSeason.springStartTime2"></a-select>
                </a-col>
                <a-col :span="12">
                  <div class="text-font-15">结束时间</div>
                  <a-select style="width: 60px; margin-right: 10px" :options="startoptions"
                    v-model:value="automaticSeason.springEndTime1"></a-select>
                  <a-select style="width: 60px" :options="endoptions"
                    v-model:value="automaticSeason.springEndTime2"></a-select>
                </a-col>
              </a-row>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card title="夏季">
              <a-row :gutter="8">
                <a-col :span="12">
                  <div class="text-font-15">开始时间</div>
                  <a-select style="width: 60px; margin-right: 10px" :options="startoptions"
                    v-model:value="automaticSeason.summerStartTime1"></a-select>
                  <a-select style="width: 60px" :options="endoptions"
                    v-model:value="automaticSeason.summerStartTime2"></a-select>
                </a-col>
                <a-col :span="12">
                  <div class="text-font-15">结束时间</div>
                  <a-select style="width: 60px; margin-right: 10px" :options="startoptions"
                    v-model:value="automaticSeason.summerEndTime1"></a-select>
                  <a-select style="width: 60px" :options="endoptions"
                    v-model:value="automaticSeason.summerEndTime2"></a-select>
                </a-col>
              </a-row>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card title="(秋季)过渡季">
              <a-row :gutter="8">
                <a-col :span="12">
                  <div class="text-font-15">开始时间</div>
                  <a-select style="width: 60px; margin-right: 10px" :options="startoptions"
                    v-model:value="automaticSeason.autumnStartTime1"></a-select>
                  <a-select style="width: 60px" :options="endoptions"
                    v-model:value="automaticSeason.autumnStartTime2"></a-select>
                </a-col>
                <a-col :span="12">
                  <div class="text-font-15">结束时间</div>
                  <a-select style="width: 60px; margin-right: 10px" :options="startoptions"
                    v-model:value="automaticSeason.autumnEndTime1"></a-select>
                  <a-select style="width: 60px" :options="endoptions"
                    v-model:value="automaticSeason.autumnEndTime2"></a-select>
                </a-col>
              </a-row>
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card title="冬季">
              <a-row :gutter="8">
                <a-col :span="12">
                  <div class="text-font-15">开始时间</div>
                  <a-select style="width: 60px; margin-right: 10px" :options="startoptions"
                    v-model:value="automaticSeason.winterStartTime1"></a-select>
                  <a-select style="width: 60px" :options="endoptions"
                    v-model:value="automaticSeason.winterStartTime2"></a-select>
                </a-col>
                <a-col :span="12">
                  <div class="text-font-15">结束时间</div>
                  <a-select style="width: 60px; margin-right: 10px" :options="startoptions"
                    v-model:value="automaticSeason.winterEndTime1"></a-select>
                  <a-select style="width: 60px" :options="endoptions"
                    v-model:value="automaticSeason.winterEndTime2"></a-select>
                </a-col>
              </a-row>
            </a-card>
          </a-col>
        </a-row>
      </div>
      <a-divider style="height: 2px; background-color: #f5f5f5" />
      <div class="text-font-18 padding-bottom-10">工作状态</div>
      <span class="text-font-16 padding-20">状态切换：</span>
      <a-radio-group v-model:value="settingsinfo.workingCondition" name="radioGroup">
        <a-radio :value="0">关闭</a-radio>
        <a-radio :value="1">运行</a-radio>
        <a-radio :value="2">模式</a-radio>
      </a-radio-group>
      <div class="padding-20" v-if="settingsinfo.workingCondition == 2">
        <a-card title="自动设置">
          <a-radio-group class="padding-bottom-10" v-model:value="settingsinfo.automaticWorking" name="radioGroup">
            <a-radio :value="1">值班</a-radio>
            <a-radio :value="2">假期</a-radio>
          </a-radio-group>
          <a-card v-if="settingsinfo.automaticWorking == 1" title="值班时间设置">
            <div @contextmenu.prevent @selectstart.prevent>
              <a-row v-for="day in days" :key="day">
                <a-col span="24">
                  <h3>{{ day }}</h3>
                  <a-row :gutter="[6, 6]">
                    <a-col v-for="time in times" :key="time" span="1">
                      <div class="time-slot" :class="{ selected: isSelected(day, time) }"
                        @mousedown="startSelection(day, time)" @mouseover="extendSelection(day, time)"
                        @mouseup="endSelection" @click="toggleSelection(day, time)">
                        {{ time }}
                      </div>
                    </a-col>
                  </a-row>
                </a-col>
              </a-row>
            </div>
          </a-card>
          <a-card v-if="settingsinfo.automaticWorking == 2" title="假期">
            <a-row>
              <a-col :span="12">
                <span class="text-font-16 padding-right-20">开始时间：</span>
                <a-date-picker v-model:value="settingsinfo.automaticWorkingVacationStatusTime
                  " value-format="YYYY-MM-DD" />
              </a-col>
              <a-col :span="12">
                <span class="text-font-16 padding-right-20">结束时间：</span>
                <a-date-picker v-model:value="settingsinfo.automaticWorkingVacationEndTime"
                  value-format="YYYY-MM-DD" /></a-col>
            </a-row>
          </a-card>
        </a-card>
      </div>
      <div style="text-align: right">
        <a-button type="primary" @click="save">保存</a-button>
      </div>
    </a-card>
    <div style="text-align: right; margin: 10px 20px">
      <a-space>
        <a-button @click="settiingshow = false">关闭</a-button>
      </a-space>
    </div>
  </div>
</template>
<script setup>
import {
  ref,
  reactive,
  watch,
  h,
  defineEmits,
  onMounted,
  onUnmounted,
  onBeforeUnmount,
  inject,
  computed
} from 'vue'
import project from '@/api/methods/project'
import projectOverallSettings from '@/api/methods/projectOverallSettings'
import { useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
const userInfo = inject('userInfo')
const route = useRoute()
const info = ref({})
const settingsinfo = ref({})
const onlineinfo = ref({})
const automaticSeason = ref({})
const currentTimeRef = ref(new Date())
const deviceErrorlist=ref([])

// 当前时间计算属性
const currentTime = computed(() => {
  const now = currentTimeRef.value
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
})

// 更新时间的定时器
let timeInterval = null

// 在组件挂载时启动定时器
onMounted(() => {
  timeInterval = setInterval(() => {
    currentTimeRef.value = new Date()
  }, 1000)
})

// 在组件卸载前清除定时器
onBeforeUnmount(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
const startoptions = ref(
  Array.from({ length: 12 }, (_, i) => ({
    value: i + 1,
    label: String(i + 1).padStart(2, '0'),
  })),
)
const endoptions = ref(
  Array.from({ length: 31 }, (_, i) => ({
    value: i + 1,
    label: String(i + 1).padStart(2, '0'),
  })),
)
// 维护每一天选中的时间列表
const selectedTimes = ref({})

// 定义数据
const days = ref(['周一', '周二', '周三', '周四', '周五', '周六', '周日'])
const times = ref([
  '00:00',
  '00:30',
  '01:00',
  '01:30',
  '02:00',
  '02:30',
  '03:00',
  '03:30',
  '04:00',
  '04:30',
  '05:00',
  '05:30',
  '06:00',
  '06:30',
  '07:00',
  '07:30',
  '08:00',
  '08:30',
  '09:00',
  '09:30',
  '10:00',
  '10:30',
  '11:00',
  '11:30',
  '12:00',
  '12:30',
  '13:00',
  '13:30',
  '14:00',
  '14:30',
  '15:00',
  '15:30',
  '16:00',
  '16:30',
  '17:00',
  '17:30',
  '18:00',
  '18:30',
  '19:00',
  '19:30',
  '20:00',
  '20:30',
  '21:00',
  '21:30',
  '22:00',
  '22:30',
  '23:00',
  '23:30',
])

const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
//运行模式
const yxmodel = ref('')
//系统状态
const systatus = ref('')
const init = () => {
  // 添加时间戳，防止浏览器缓存
  const timestamp = new Date().getTime()

  project
    .detail({ id: props.recordwhere.projectId, _t: timestamp })
    .then(res => {
      info.value = res.data
    })
  projectOverallSettings
    .getByProjectId({ projectId: props.recordwhere.projectId, _t: timestamp })
    .then(res => {
      var projectOverallSettings = res.data.projectOverallSettings
      projectOverallSettings.automaticWorkingVacationStatusTime = dayjs(
        projectOverallSettings.automaticWorkingVacationStatusTime,
        'YYYY-MM-DD',
      )
      projectOverallSettings.automaticWorkingVacationEndTime = dayjs(
        projectOverallSettings.automaticWorkingVacationEndTime,
        'YYYY-MM-DD',
      )
      settingsinfo.value = projectOverallSettings
      var as = res.data.automaticSeason
      var springStartTime = as.springStartTime.split('-')
      var springEndTime = as.springEndTime.split('-')
      var summerStartTime = as.summerStartTime.split('-')
      var summerEndTime = as.summerEndTime.split('-')
      var autumnStartTime = as.autumnStartTime.split('-')
      var autumnEndTime = as.autumnEndTime.split('-')
      var winterStartTime = as.winterStartTime.split('-')
      var winterEndTime = as.winterEndTime.split('-')
      var seltime = {
        springStartTime1: parseInt(springStartTime[0]),
        springStartTime2: parseInt(springStartTime[1]),
        springEndTime1: parseInt(springEndTime[0]),
        springEndTime2: parseInt(springEndTime[1]),
        summerStartTime1: parseInt(summerStartTime[0]),
        summerStartTime2: parseInt(summerStartTime[1]),
        summerEndTime1: parseInt(summerEndTime[0]),
        summerEndTime2: parseInt(summerEndTime[1]),
        autumnStartTime1: parseInt(autumnStartTime[0]),
        autumnStartTime2: parseInt(autumnStartTime[1]),
        autumnEndTime1: parseInt(autumnEndTime[0]),
        autumnEndTime2: parseInt(autumnEndTime[1]),
        winterStartTime1: parseInt(winterStartTime[0]),
        winterStartTime2: parseInt(winterStartTime[1]),
        winterEndTime1: parseInt(winterEndTime[0]),
        winterEndTime2: parseInt(winterEndTime[1]),
      }
      automaticSeason.value = seltime
      selectedTimes.value = res.data.dutyTimeSetting
      yxmodel.value = projectOverallSettings.seasonMode == 0 ? '手动' : '自动'
      systatus.value =
        projectOverallSettings.workingCondition == 0
          ? '关闭'
          : projectOverallSettings.workingCondition == 1
            ? '运行'
            : projectOverallSettings.workingCondition == 2
              ? projectOverallSettings.automaticWorking == 1
                ? '值班'
                : projectOverallSettings.automaticWorking == 2
                  ? '值班'
                  : '未知'
              : '未知'
    })
}
const getRedisProjectInfoByProjectId = () => {
  projectOverallSettings
    .getRedisProjectInfoByProjectId({ projectId: props.recordwhere.projectId })
    .then(res => {
      if (res.data != null) {
        onlineinfo.value = res.data.redisProjectInfo
        deviceErrorlist.value = res.data.deviceErrorlist || []
        settingsinfo.value.outdoorTemperature = res.data.redisProjectInfo.outdoorTemperature
        settingsinfo.value.outdoorHumidity = res.data.redisProjectInfo.outdoorHumidity
      }
    })
}
init()
getRedisProjectInfoByProjectId()
const handleMenuClick = ({ key }) => {
  changeActive(2, key)
}

const settiingshow = ref(false)

const settiingclick = () => {
  if (!userInfo.value.isConfig) {
    message.error('暂无配置权限，无法进行操作')
    return
  }
  settiingshow.value = true
}

// 初始化每一天的选中时间为一个空数组
days.value.forEach(day => {
  selectedTimes.value[day] = []
})

// 鼠标按下状态和当前选中开始的时间
let isSelecting = false
let selectionStartDay = null
let selectionStartTime = null

// 开始选中
const startSelection = (day, time) => {
  isSelecting = true
  selectionStartDay = day
  selectionStartTime = time
  toggleSelection(day, time) // 立即选中点击的时间
}

// 扩展选中范围
const extendSelection = (day, time) => {
  if (isSelecting && selectionStartDay === day) {
    // 如果是同一行（同一天），则扩展选中
    const startIndex = times.value.indexOf(selectionStartTime)
    const currentIndex = times.value.indexOf(time)
    if (startIndex !== -1 && currentIndex !== -1) {
      for (
        let i = Math.min(startIndex, currentIndex);
        i <= Math.max(startIndex, currentIndex);
        i++
      ) {
        const currentTime = times.value[i]
        if (!selectedTimes.value[day].includes(currentTime)) {
          selectedTimes.value[day].push(currentTime)
        }
      }
    }
  }
}

// 结束选中
const endSelection = () => {
  isSelecting = false
  selectionStartDay = null
  selectionStartTime = null
}

// 切换选中状态（单击选择）
const toggleSelection = (day, time) => {
  if (isSelecting) return // 如果正在多选，则不响应单击事件
  const isSelected = selectedTimes.value[day].includes(time)
  if (isSelected) {
    // 如果已经选中，则取消选中
    selectedTimes.value[day] = selectedTimes.value[day].filter(t => t !== time)
  } else {
    // 否则添加到选中列表
    selectedTimes.value[day].push(time)
  }
}

// 定义定时器变量
let timer = null
// 启动定时器
const startTimer = () => {
  timer = setInterval(() => {
    getRedisProjectInfoByProjectId()
  }, 5000) // 每10秒刷新一次数据
}

// 判断是否选中
const isSelected = (day, time) => {
  return selectedTimes.value[day]?.includes(time) || false
}
// 监听全局 mouseup 事件以确保在组件外释放鼠标也能结束选中
onMounted(() => {
  window.addEventListener('mouseup', endSelection)
  startTimer() // 启动定时刷新
})
onUnmounted(() => {
  window.removeEventListener('mouseup', endSelection)
})
// 在组件销毁前清除定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer) // 清除定时器，防止内存泄漏
  }
})
const formatDate = input => dayjs(input).format('YYYY-MM-DD')
const save = () => {
  const result = Object.keys(selectedTimes.value).map((key, index) => {
    return { Cycle: index + 1, Time: selectedTimes.value[key] }
  })

  settingsinfo.value.springStartTime =
    String(automaticSeason.value.springStartTime1).padStart(2, '0') +
    '-' +
    String(automaticSeason.value.springStartTime2).padStart(2, '0')

  settingsinfo.value.springEndTime =
    String(automaticSeason.value.springEndTime1).padStart(2, '0') +
    '-' +
    String(automaticSeason.value.springEndTime2).padStart(2, '0')

  settingsinfo.value.summerStartTime =
    String(automaticSeason.value.summerStartTime1).padStart(2, '0') +
    '-' +
    String(automaticSeason.value.summerStartTime2).padStart(2, '0')

  settingsinfo.value.summerEndTime =
    String(automaticSeason.value.summerEndTime1).padStart(2, '0') +
    '-' +
    String(automaticSeason.value.summerEndTime2).padStart(2, '0')

  settingsinfo.value.autumnStartTime =
    String(automaticSeason.value.autumnStartTime1).padStart(2, '0') +
    '-' +
    String(automaticSeason.value.autumnStartTime1).padStart(2, '0')

  settingsinfo.value.autumnEndTime =
    String(automaticSeason.value.autumnEndTime1).padStart(2, '0') +
    '-' +
    String(automaticSeason.value.autumnEndTime2).padStart(2, '0')

  settingsinfo.value.winterStartTime =
    String(automaticSeason.value.winterStartTime1).padStart(2, '0') +
    '-' +
    String(automaticSeason.value.winterStartTime2).padStart(2, '0')

  settingsinfo.value.winterEndTime =
    String(automaticSeason.value.winterEndTime1).padStart(2, '0') +
    '-' +
    String(automaticSeason.value.winterEndTime2).padStart(2, '0')

  settingsinfo.value.automaticWorkingVacationStatusTime = formatDate(
    settingsinfo.value.automaticWorkingVacationStatusTime,
  )
  settingsinfo.value.automaticWorkingVacationEndTime = formatDate(
    settingsinfo.value.automaticWorkingVacationEndTime,
  )

  projectOverallSettings.update(settingsinfo.value).then(res => {
    var data = {
      projectId: props.recordwhere.projectId,
      timeSetting: result,
    }
    projectOverallSettings.updateDutyTimeSetting(data).then(res => {
      message.success('成功', 1, () => {
        // onClose()
        // emit('updateData') // 触发事件
        init()
      })
    })
  })
}
// 定义子组件事件
const emit = defineEmits(['change-active'])

const changeActive = (value, zoneType) => {
  emit('change-active', value, zoneType)
}
</script>
<style scoped>
.time-slot {
  padding: 8px;
  text-align: center;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.time-slot:hover {
  background-color: #e0e0e0;
  /* 悬停时的颜色 */
}

.time-slot.selected {
  background-color: #1677ff;
  /* 选中时的颜色 */
  color: white;
}
</style>
