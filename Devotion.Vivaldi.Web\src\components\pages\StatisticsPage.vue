<template>
  <div>
    <a-form :model="formInfo" ref="formRef" layout="Inline" @finish="onFinish">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item label="房间：">
            <a-select v-model:value="formInfo.sensorConfigId" placeholder="请选择" :options="sensorConfigdata">
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="日期">
            <a-date-picker style="width: 100%" v-model:value="formInfo.time" value-format="YYYY-MM-DD" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item>
            <a-button type="primary" html-type="submit">查询</a-button>
          </a-form-item></a-col>
      </a-row>
    </a-form>
  </div>
  <div>
    <a-card title="室外环境">
      <div>温度</div>
      <div ref="outdoorTemperature_container"></div>
      <div>湿度</div>
      <div ref="outdoorHumidity_container"></div>
    </a-card>
    <a-card title="室内环境">
      <div>实际作用温度</div>
      <div ref="temperature_container"></div>
      <div>实际相对湿度</div>
      <div ref="humidity_container"></div>
      <div>实际露点</div>
      <div ref="dewPoint_container"></div>
    </a-card>
    <a-card title="水系统">
      <div>供水水温</div>
      <div ref="waterSupplyTemperature_container"></div>
      <div>回水水温</div>
      <div ref="returnWaterTemperature_container"></div>
    </a-card>
    <a-card title="新风机">
      <div>送风风温</div>
      <div ref="supplyAirTemperature_container"></div>
      <div>送风露点</div>
      <div ref="dewPointTemperature_container"></div>
      <div>进风风量</div>
      <div ref="inletAirFlow_container"></div>
      <div>排风风量</div>
      <div ref="exhaustAirFlow_container"></div>
    </a-card>
  </div>
</template>
<script setup>
import sensorConfig from '@/api/methods/sensorConfig'
import statistics from '@/api/methods/statistics'
import {
  defineProps,
  defineEmits,
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
} from 'vue'
import { Chart } from '@antv/g2'
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const formInfo = reactive({
  sensorConfigId: null,
  time: null,
})
const sensorConfigdata = ref([])
const outdoorTemperature_container = ref(null)
const outdoorHumidity_container = ref(null)
const temperature_container = ref(null)
const humidity_container = ref(null)
const dewPoint_container = ref(null)
const waterSupplyTemperature_container = ref(null)
const returnWaterTemperature_container = ref(null)

const supplyAirTemperature_container = ref(null)
const dewPointTemperature_container = ref(null)
const inletAirFlow_container = ref(null)
const exhaustAirFlow_container = ref(null)
const init = () => {
  sensorConfig
    .getOptionsList({ projectId: props.recordwhere.projectId })
    .then(res => {
      sensorConfigdata.value = res.data
    })
}

const total = () => {
  statistics.gettotal({ sensorConfigId: formInfo.sensorConfigId, date: formInfo.time }).then(res => {
    renderBarOutdoorTemperatureChart(res.data.outdoorTemperature)
    renderBarOutdoorHumidityChart(res.data.outdoorHumidity)
    renderBarTemperatureChart(res.data.temperature)
    renderBarHumidityChart(res.data.humidity)
    renderBarDewPointChart(res.data.dewPoint)
    renderBarWaterSupplyTemperatureChart(res.data.waterSupplyTemperature)
    renderBarReturnWaterTemperatureChart(res.data.returnWaterTemperature)

    renderBarSupplyAirTemperatureChart(res.data.supplyAirTemperature)
    renderBardewPointTemperatureChart(res.data.dewPointTemperature)
    renderBarInletAirFlowChart(res.data.inletAirFlow)
    renderBarExhaustAirFlowChart(res.data.exhaustAirFlow)

  })

}

init()

onMounted(() => {
  // outdoorTemperaturechart = renderBarChart(outdoorTemperature_container.value)
  //init()
})

function onClick() {
  updateBarChart(chart)
}

function renderBarOutdoorTemperatureChart(data) {
  const chart = new Chart({
    container: outdoorTemperature_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    }).scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + '°C', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart.point().encode('shape', 'point').tooltip(false)
  chart.render()
  // 如上
}
function renderBarOutdoorHumidityChart(data) {
  const chart = new Chart({
    container: outdoorHumidity_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    })
    .scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + '%', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart
    .point()
    .encode('shape', 'point')
    .tooltip(false);
  chart.render()
  // 如上
}
function renderBarTemperatureChart(data) {
  const chart = new Chart({
    container: temperature_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    }).scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + '°C', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart.point().encode('shape', 'point').tooltip(false)
  chart.render()
  // 如上
}
function renderBarHumidityChart(data) {
  const chart = new Chart({
    container: humidity_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    })
    .scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + '%', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart
    .point()
    .encode('shape', 'point')
    .tooltip(false);
  chart.render()
  // 如上
}
function renderBarDewPointChart(data) {
  const chart = new Chart({
    container: dewPoint_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    }).scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + '°C', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart.point().encode('shape', 'point').tooltip(false)
  chart.render()
  // 如上
}
function renderBarWaterSupplyTemperatureChart(data) {
  const chart = new Chart({
    container: waterSupplyTemperature_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    }).scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + '°C', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart.point().encode('shape', 'point').tooltip(false)
  chart.render()
  // 如上
}
function renderBarReturnWaterTemperatureChart(data) {
  const chart = new Chart({
    container: returnWaterTemperature_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    }).scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + '°C', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart.point().encode('shape', 'point').tooltip(false)
  chart.render()
  // 如上
}
function renderBarSupplyAirTemperatureChart(data) {
  const chart = new Chart({
    container: supplyAirTemperature_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    }).scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + '°C', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart.point().encode('shape', 'point').tooltip(false)
  chart.render()
  // 如上
}
function renderBardewPointTemperatureChart(data) {
  const chart = new Chart({
    container: dewPointTemperature_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    }).scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + '°C', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart.point().encode('shape', 'point').tooltip(false)
  chart.render()
  // 如上
}
function renderBarInletAirFlowChart(data) {
  const chart = new Chart({
    container: inletAirFlow_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    }).scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + 'm³/h ', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart.point().encode('shape', 'point').tooltip(false)
  chart.render()
  // 如上
}
function renderBarExhaustAirFlowChart(data) {
  const chart = new Chart({
    container: exhaustAirFlow_container.value,
    autoFit: true,
  })

  chart
    .data(data)
    .encode('x', 'time')
    .encode('y', 'value')
    .encode('color', 'title')
    .scale('x', {
      range: [0, 1],
    })
    .scale('y', {
      nice: true,
    }).scale('color', {
      range: ['#fa8c16', '#1677ff', '#52c41a'], // 为三条折线设置不同的颜色
    })
    .axis('y', { labelFormatter: d => d + 'm³/h ', title: null, labelFill: 'black', labelOpacity: 0.7 })
    .axis('x', { title: null, labelFill: 'black', labelOpacity: 0.7 })
    .line()
    .encode('shape', 'smooth')
  chart.point().encode('shape', 'point').tooltip(false)
  chart.render()
  // 如上
}

function updateBarChart(chart) {
  // 如上
}
const onFinish = values => {
  total()
}

</script>
<style scoped></style>
