<!-- components/FullscreenLoading.vue -->
<template>
  <div v-if="visible" class="fullscreen-loading">
    <ASpin :tip="text" :size="size">
      <template #indicator>
        <LoadingOutlined :style="{ fontSize: iconSize }" spin />
      </template>
    </ASpin>
  </div>
</template>

<script setup>
import { Spin } from 'ant-design-vue'
import { LoadingOutlined } from '@ant-design/icons-vue'

defineProps({
  visible: Boolean,
  text: String,
  size: {
    type: String,
    default: 'large'
  },
  iconSize: {
    type: String,
    default: '32px'
  }
})

// 显式注册组件
defineOptions({
  components: {
    ASpin: Spin,
    LoadingOutlined
  }
})
</script>
