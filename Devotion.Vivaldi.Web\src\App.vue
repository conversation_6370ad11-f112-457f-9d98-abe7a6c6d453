<template>
  <a-config-provider :locale="localeStore.locale">
    <section id="app">
      <router-view />
    </section>
  </a-config-provider>
</template>

<style></style>

<script setup>
import { configStore } from '@//stores/config'
import { dictStore } from '@//stores/dict'
import { useUserStore } from '@/stores/user'
import { provide, computed } from 'vue'
const localeStore = configStore()
const userStore = useUserStore()
const dict = dictStore()
dict.initDictList()
// 提供一个响应式的 userInfo
provide(
  'userInfo',
  computed(() => userStore.userInfo),
)
</script>
