<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="community"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    :ftableDetails="true"
    @edit="edit"
    @details="details"
    ref="childRef"
  >
    <!-- 可以通过插槽自定义单元格 -->
    <template #custom-provincecitydistrict="{ record }">
      {{ record.province + record.city + record.district }}
    </template>
    <template #custom-houseType="{ record }">
      <span
        v-html="dictTemplate.tabletempInt('houseType', record.houseType)"
      ></span>
    </template>
  </form-table>
  <!-- 新增修改 -->
  <CommunityEdit
    :open="editopen"
    @close="editopen = false"
    @updateData="refreshData"
    ref="editRef"
  >
  </CommunityEdit>

  <!-- 详情 -->
  <CommunityDetails
    :open="detailsopen"
    :info="info"
    @close="detailsopen = false"
  >
  </CommunityDetails>
</template>
<script setup>
import { ref, reactive } from 'vue'
import FormTable from '../../components/FormTable.vue'
import CommunityDetails from '../../components/details/CommunityDetails.vue'
import CommunityEdit from '../../components/edit/CommunityEdit.vue'
import dictTemplate from '@/utils/dictTemplate'
import area from '@/utils/area'
import community from '@/api/methods/community'
const allProvince = ref(area.getAllProvince())
const allCity = ref(area.getAllCity())
const allAreas = ref(area.getAllAreas())
const formState = ref({
  communityName: { label: '小区名称', value: '', type: 'text' },
  province: {
    label: '省',
    value: null,
    type: 'select',
    data: allProvince,
  },
  city: {
    label: '市',
    value: null,
    type: 'select',
    data: allCity,
  },
  district: {
    label: '区',
    value: null,
    type: 'select',
    data: allAreas,
  },
  detailedAddress: { label: '详细地址', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '小区名称',
    dataIndex: 'communityName',
    key: 'communityName',
  },
  {
    title: '房屋类型',
    dataIndex: 'houseType',
    key: 'houseType',
  },
  {
    title: '省市区',
    dataIndex: 'provincecitydistrict',
    key: 'provincecitydistrict',
    width: 300,
  },
  {
    title: '详细地址',
    dataIndex: 'detailedAddress',
    key: 'detailedAddress',
  },
  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)

//编辑
const editopen = ref(false)
const editRef = ref(null)
const edit = record => {
  editRef.value.init(record.id)
  editopen.value = true
}
const refreshData = () => {
  childRef.value.tableLoad()
}
//详情
const detailsopen = ref(false)
const info = ref({})
const details = record => {
  community.get({ id: record.id }).then(res => {
    info.value = res.data
    detailsopen.value = true
  })
}
</script>
<style></style>
