<template>
  <!-- 详情 -->
  <a-drawer
    :title="editTitle"
    width="60%"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item
            label="分区编号"
            name="zoneNo"
            :rules="[{ required: true, message: '分区编号不能为空' }]"
          >
            <a-select
              v-model:value="formInfo.zoneNo"
              placeholder="请选择"
              :options="formInfo.zoneNoOptions"
              @change="zoneNoChange"
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label="分区名称"
            name="zoneName"
            :rules="[{ required: true, message: '分区名称不能为空' }]"
          >
            <a-input v-model:value="formInfo.zoneName" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label="水泵名称"
            name="waterPumpName"
            :rules="[{ required: true, message: '水泵名称不能为空' }]"
          >
            <a-input
              v-model:value="formInfo.waterPumpName"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label="水泵DO"
            name="waterPumpDO"
            :rules="[{ required: true, message: '水泵DO不能为空' }]"
          >
            <a-input-number
              :min="0"
              :max="80"
              :precision="0"
              v-model:value="formInfo.waterPumpDO"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item
        label="测水温端口（PT1000）和 混水阀端口"
        name="supplyWaterTemperaturePort"
        :rules="[{ required: true, message: '不能为空' }]"
      >
        <a-input-number
          :min="0"
          :max="16"
          :precision="0"
          v-model:value="formInfo.supplyWaterTemperaturePort"
          placeholder="请输入"
        />
      </a-form-item>
      <a-row :gutter="16" class="padding-bottom-20">
        <a-col :span="6">
          供水水温端口: {{ formInfo.supplyWaterTemperaturePort }}</a-col
        >
        <a-col :span="6">
          回水水温端口: {{ formInfo.supplyWaterTemperaturePort }}
        </a-col>
        <a-col :span="6">
          混水阀端口: {{ formInfo.supplyWaterTemperaturePort }}
        </a-col>
        <a-col :span="6">
          接收混水阀信息端口: {{ formInfo.supplyWaterTemperaturePort }}
        </a-col>
      </a-row>

      <a-row :gutter="16" v-if="formInfo.zoneType == 2">
        <a-col :span="12">
          <a-form-item label="逻辑属性" name="logicalProperty">
            <a-radio-group
              v-model:value="formInfo.logicalProperty"
              name="radioGroup"
            >
              <a-radio :value="1">独立运行</a-radio>
              <a-radio :value="2">天棚混合运行</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <!-- <a-col :span="12" v-if="formInfo.logicalProperty == 2">
          <a-form-item label="混合运行模式" name="zoneName">
            <a-checkbox-group v-model:value="formInfo.mixedOperationMode">
              <a-checkbox value="1" v-if="formInfo.zoneType != 1" name="type"
                >天棚</a-checkbox
              >
              <a-checkbox value="2" v-if="formInfo.zoneType != 2" name="type"
                >地暖</a-checkbox
              >
              <a-checkbox value="3" v-if="formInfo.zoneType != 3" name="type"
                >风盘</a-checkbox
              >
            </a-checkbox-group>
          </a-form-item>
        </a-col> -->
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="房间选择" name="roomlist">
            <a-checkbox-group
              name="checkboxgroup"
              :options="formInfo.roomOptions"
              @change="roomChange"
              v-model:value="formInfo.selectedRooms"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <div>
        <a-table
          :dataSource="formInfo.thermoelectricValve"
          :columns="columns"
          :pagination="false"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'thermoelectricValveDO'">
              <a-input-number
                style="width: 100%"
                :min="0"
                :max="80"
                :precision="0"
                v-model:value="record.thermoelectricValveDO"
                placeholder="请输入"
              />
            </template>
            <template v-if="column.key === 'thermoelectricValveName'">
              <a-input
                v-model:value="record.thermoelectricValveName"
                placeholder="请输入"
              />
            </template>
            <template v-if="column.key === 'operation'">
              <a-button
                type="primary"
                danger
                @click="handleDelete(record.sensorConfigId)"
                >删除电磁阀</a-button
              >
            </template>
          </template>
        </a-table>
      </div>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup>
import { defineProps, defineEmits, ref, h } from 'vue'
import { message } from 'ant-design-vue'
import radiationZone from '@/api/methods/radiationZone'
const props = defineProps({
  editTitle: {
    type: String,
    default: '新增',
  },
  open: {
    type: Boolean,
    required: true,
  },
  formInfo: {
    type: Object,
    required: true,
  },
})
const formRef = ref(null)
// 选中的房间
const columns = [
  {
    title: '房间名称',
    dataIndex: 'name',
    key: 'name',
    width: 150,
  },
  {
    title: '热电阀DO',
    dataIndex: 'thermoelectricValveDO',
    key: 'thermoelectricValveDO',
    width: 250,
  },
  {
    title: '热电阀名称',
    dataIndex: 'thermoelectricValveName',
    key: 'thermoelectricValveName',
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
]

//房间选择事件
const roomChange = value => {
  const filteredOptions = props.formInfo.roomOptions.filter(
    option => !option.disabled,
  )
  // 获取选中项的 label
  const selectedLabels = value
    .map(v => {
      const option = filteredOptions.find(option => option.value === v)
      return option
    })
    .filter(option => option !== undefined)
  var roomdata = []

  selectedLabels.forEach(x => {
    roomdata.push({
      id: '',
      sensorConfigId: x.value,
      name: x.label,
      thermoelectricValveDO: 0,
      thermoelectricValveName:
        x.label +
        '_' +
        (props.formInfo.zoneType == 1
          ? '天棚_开关'
          : props.formInfo.zoneType == 2
            ? '地板_开关'
            : '风盘_开关' + ''),
    })
  })
  props.formInfo.thermoelectricValve = roomdata
  props.formInfo.thermoelectricValve = roomdata
}

// 自定义删除功能
const handleDelete = id => {
  // 找到要删除的记录的索引
  const index = props.formInfo.thermoelectricValve.findIndex(
    item => item.sensorConfigId === id,
  )
  if (index !== -1) {
    // 从数据源中移除该记录
    props.formInfo.thermoelectricValve.splice(index, 1)
  }

  // 从选中的房间列表中移除记录
  const selectedRoomsIndex = selectedRooms.value.indexOf(id)
  if (selectedRoomsIndex !== -1) {
    selectedRooms.value.splice(selectedRoomsIndex, 1)
  }
}

const onSave = () => {
  // 提交数据
  formRef.value.validate().then(() => {
    if (props.formInfo.thermoelectricValve.length == 0) {
      message.error('请选择房间', 1)
      return
    }
    const emptyValueItem = props.formInfo.thermoelectricValve.some(item => {
      if (item.thermoelectricValveName === '') {
        message.error(item.name + '的热电阀名称不能为空', 1)
        return true
      }
    })
    if (emptyValueItem) {
      return
    }
    if (props.formInfo.id == '') {
      radiationZone.add(props.formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    } else {
      radiationZone.update(props.formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    }
  })
}
//选中分区
const zoneNoChange = value => {
  props.formInfo.zoneName =
    props.formInfo.zoneType == '1'
      ? '天棚' + value + '区'
      : props.formInfo.zoneType == '2'
        ? '地板' + value + '区'
        : '风盘' + value + '区'

  props.formInfo.waterPumpName = props.formInfo.zoneName + '水泵'
}

// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close', 'updateData'])
const onClose = () => {
  emit('close')
}
</script>
