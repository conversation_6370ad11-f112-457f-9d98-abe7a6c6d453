<template>
  <projectSettingPage
    v-if="activeButton == 1"
    :recordwhere="recordwhere"
    @change-active="updateActiveButton"
  >
  </projectSettingPage>

  <RadiationZoneSettingPage
    v-if="activeButton == 2"
    :recordwhere="recordwhere"
    @change-active="updateActiveButton"
  >
  </RadiationZoneSettingPage>

  <FreshAirAreaSettingPage
    v-if="activeButton == 3"
    :recordwhere="recordwhere"
    @change-active="updateActiveButton"
  >
  </FreshAirAreaSettingPage>
  <ColdHeatSourcesSettingPage
    v-if="activeButton == 4"
    :recordwhere="recordwhere"
    @change-active="updateActiveButton"
  >
  </ColdHeatSourcesSettingPage>
</template>
<script setup>
import { ref, reactive, watch, onMounted, onBeforeUnmount } from 'vue'
import projectSettingPage from '../../components/pages/projectSettingPage.vue'
import RadiationZoneSettingPage from '../../components/pages/RadiationZoneSettingPage.vue'
import ColdHeatSourcesSettingPage from '../../components/pages/ColdHeatSourcesSettingPage.vue'
import FreshAirAreaSettingPage from '../../components/pages/FreshAirAreaSettingPage.vue'
import pcport from '@/api/methods/pcport'
import projectOverallSettings from '@/api/methods/projectOverallSettings'
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const info = ref({})
const settingsinfo = ref({})
const activeButton = ref(1)
const recordwhere = ref({ projectId: props.recordwhere.projectId })
const selectHex = () => {
  pcport.selectHex({ projectId: props.recordwhere.projectId }).then(res => {})
}

selectHex()

// 定义定时器变量
let timer = null
// 启动定时器
const startTimer = () => {
  timer = setInterval(() => {
    selectHex()
  }, 600000)
}

// 在组件挂载后启动定时器
onMounted(() => {
  startTimer()
})

// 在组件销毁前清除定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }
})

// 更新父组件状态的方法
const updateActiveButton = (value, zoneType) => {
  activeButton.value = value
  if (value == 2) {
    recordwhere.value.zoneType = zoneType
  }
}
</script>

<style></style>
