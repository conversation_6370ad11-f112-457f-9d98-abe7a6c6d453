import { post, get } from '@/api/request'

const community = {
  //根据id获取信息
  get(params) {
    return get('community/get', params, true)
  },
  //保存
  save(params) {
    return post('community/save', params, true)
  },
  //新增
  add(params) {
    return post('community/add', params, true)
  },
  //更新
  update(params) {
    return post('community/update', params)
  },
  //获取小区列表信息
  getList() {
    return get('community/getList')
  },
}
//
export default community
