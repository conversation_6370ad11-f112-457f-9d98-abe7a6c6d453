<template>
  <div class="app">
    <div class="app-l">
      <img class="logo1" src="@/assets/imgs/logo1.png" />
      <img class="logo_footer" src="@/assets/imgs/logo_footer.png" />
    </div>
    <div class="app-r">
      <div class="header">欢迎使用VIVALDI后台管理系统</div>
      <div class="demo-login">
        <div class="tel flex-row">登录</div>
        <a-form
          :model="formState"
          name="normal_login"
          class="login-form"
          @finish="onFinish"
          @finishFailed="onFinishFailed"
        >
          <a-form-item
            name="mobile"
            :rules="[{ required: true, message: '手机号码不能为空!' }]"
          >
            <a-input
              v-model:value="formState.mobile"
              placeholder="请输入手机号码"
              size="large"
            >
              <template #prefix>
                <MobileOutlined class="site-form-item-icon" />
              </template>
            </a-input>
          </a-form-item>
          <a-form-item
            name="password"
            :rules="[{ required: true, message: '密码不能为空!' }]"
          >
            <a-input-password
              v-model:value="formState.password"
              placeholder="请输入密码"
              size="large"
            >
              <template #prefix>
                <LockOutlined class="site-form-item-icon" />
              </template>
            </a-input-password>
          </a-form-item>
          <a-form-item>
            <a-form-item name="remember" no-style>
              <a-checkbox v-model:checked="formState.remember"
                >自动登录</a-checkbox
              >
            </a-form-item>
          </a-form-item>

          <a-form-item>
            <a-button
              :disabled="disabled"
              type="primary"
              :loading="loading"
              html-type="submit"
              size="large"
              class="login-form-button"
            >
              登录
            </a-button>
          </a-form-item>
        </a-form>
      </div>
      <div class="footer">
        <span> Copyright@维瓦尔第环境技术有限公司 支持电话：************</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { login } from '@/api/methods/login'
import sysMenu from '@/api/methods/sysMenu'
import sysUser from '@/api/methods/sysUser'
import router from '@/router'
import { useUserStore } from '@/stores/user'
import { menuStore } from '@/stores/menu'
import { reactive, computed, ref } from 'vue'
import { message } from 'ant-design-vue'
const formState = reactive({
  mobile: '',
  password: '',
  remember: true,
  vercode: '',
})
const loading = ref(false)
const user = useUserStore()
const menu = menuStore()
const activeKey = ref('1')
//登录
const onFinish = values => {
  loading.value = true

  let data = {
    mobile: values.mobile,
    password: values.password,
    loginType: 0,
    vercode: '',
  }
  login(data)
    .then(async res => {
      loading.value = false
      user.token = res.data.token
      user.userInfo = {
        id: res.data.id,
        name: res.data.name,
        mobile: res.data.mobile,
        roleId: res.data.roleId,
        isConfig: res.data.isConfig,
        dealerId: res.data.dealerId
      }
      sysMenu
        .getmenu()
        .then(res => {
          menu.menus = res.data
          message.success('登录成功')
          router.push('/console')
        })
        .catch()
    })
    .catch(err => {
      loading.value = false
      console.log(err)
    })
}
const disabled = computed(() => {
  if (activeKey.value == '1') {
    return !(formState.mobile && formState.password)
  } else {
    return !(formState.mobile && formState.vercode)
  }
})
const smstext = ref('获取验证码')
const smsdisabled = ref(false)
let countdownTimer
//发送验证码
const sendSmsCode = () => {
  if (formState.mobile == '') {
    message.error('请输入手机号码')
    return
  }
  sysUser.sendCode({ mobile: formState.mobile }).then(res => {
    message.success('验证码已发送')
    smsdisabled.value = true
    smstext.value = '60s后可重发'
    let count = 60
    countdownTimer = setInterval(() => {
      count--
      smstext.value = `${count}s后可重发`
      if (count === 0) {
        clearInterval(countdownTimer)
        smsdisabled.value = false
        smstext.value = '获取验证码'
      }
    }, 1000)
  })
}
</script>
<style scoped>
.app {
  height: 100vh !important;
  display: flex !important;
  align-items: center !important;
  background-image: url(../assets/imgs/loginbackground.svg);
  background-size: 100% 100%;
}
.app-l {
  height: 100%;
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #191919;
}
.logo1 {
  width: 30%;
  margin-bottom: 80px;
}
.logo_footer {
  margin-top: 100px;
  width: 80%;
}
.app-r {
  align-items: center !important;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 50%;
}
.header {
  font-size: 40px;
  margin: 0 0 50px 0;
  letter-spacing: 6px;
  font-weight: 600;
}
.demo-login {
  width: 400px !important;
  height: 380px;
  margin: 0 auto;
  background-color: rgba(255, 255, 255, 1);
  padding: 30px;
  border-radius: 20px;
}

.demo-auto-login {
  margin-bottom: 60px;
  text-align: left;
}

.demo-auto-login a {
  float: right;
}

.tel {
  font-size: 30px;
  text-align: center;
  padding: 20px;
  color: rgb(0, 0, 0);
}

.login-form-forgot {
  float: right;
}

.login-form-button {
  width: 100%;
}
.footer {
  position: fixed;
  bottom: 20px;
  width: 100%;
  text-align: center;
  color: #191919;
  font-size: 18px;
}
</style>
