<template>
  <div class="bg-color-white padding-20">
    <div class="blockquote">辐射区</div>
    <a-table
      :columns="columns1"
      :data-source="radiationZonedata"
      bordered
      size="small"
      :pagination="false"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'waterPumpDO'">
          <a-row :gutter="8">
            <a-col :span="12"> Y_{{ record.waterPumpDO }} </a-col>
            <a-col :span="12">
              <a-space>
                <a-button
                  type="primary"
                  size="small"
                  @click="handleStatus(1, record, true)"
                  >开</a-button
                >
                <a-button
                  type="primary"
                  size="small"
                  @click="handleStatus(1, record, false)"
                  >关</a-button
                >
              </a-space>
            </a-col>
          </a-row>
        </template>
        <template v-if="column.key === 'pumpStatus'">
          {{ record.pumpStatus ? '开启' : '关闭' }}
        </template>

        <template v-if="column.key === 'zoneNo'">
          {{ record.zoneNo }}区
        </template>
        <template v-if="column.key === 'waterSupplyTemperature'">
          {{ record.waterSupplyTemperature }}℃
        </template>
        <template v-if="column.key === 'returnWaterTemperature'">
          {{ record.returnWaterTemperature }}℃
        </template>
        <template v-if="column.key === 'sensorConfigName'">
          <span
            class="padding-right-20"
            v-for="(item, index) in record.sensorConfigName"
            >{{ item }}</span
          >
        </template>
      </template>
    </a-table>
    <div class="blockquote">新风区</div>
    <a-table
      :columns="columns2"
      :data-source="freshAirFandata"
      bordered
      size="small"
      :pagination="false"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'freshAirFanPowerSupplyDO'">
          <a-row :gutter="8">
            <a-col :span="12"> Y_{{ record.freshAirFanPowerSupplyDO }} </a-col>
            <a-col :span="12">
              <a-space>
                <a-button
                  type="primary"
                  size="small"
                  @click="handleStatus(2, record, true)"
                  >开</a-button
                >
                <a-button
                  type="primary"
                  size="small"
                  @click="handleStatus(2, record, false)"
                  >关</a-button
                >
              </a-space>
            </a-col>
          </a-row>
        </template>
        <template v-if="column.key === 'highTemperaturePumpDO'">
          <a-row :gutter="8">
            <a-col :span="12"> Y_{{ record.highTemperaturePumpDO }} </a-col>
            <a-col :span="12">
              <a-space>
                <a-button
                  type="primary"
                  size="small"
                  @click="handleStatus(3, record, true)"
                  >开</a-button
                >
                <a-button
                  type="primary"
                  size="small"
                  @click="handleStatus(3, record, false)"
                  >关</a-button
                >
              </a-space>
            </a-col>
          </a-row>
        </template>
        <template v-if="column.key === 'freshAirFanStatus'">
          {{ record.freshAirFanStatus ? '开启' : '关闭' }}
        </template>
        <template v-if="column.key === 'getHighTemperaturePumpStatus'">
          {{ record.getHighTemperaturePumpStatus ? '开启' : '关闭' }}
        </template>
        <template v-if="column.key === 'zoneNo'">
          {{ record.zoneNo }}区
        </template>
        <template v-if="column.key === 'supplyAirTemperature'">
          {{ record.supplyAirTemperature }}℃
        </template>
        <template v-if="column.key === 'outdoorTemperature'">
          {{ record.outdoorTemperature }}℃
        </template>
        <template v-if="column.key === 'sensorConfigName'">
          <span
            class="padding-right-20"
            v-for="(item, index) in record.sensorConfigName"
            >{{ item }}</span
          >
        </template>
      </template>
    </a-table>
    <div class="blockquote">房间</div>
    <a-table
      :columns="columns3"
      :data-source="sensorConfigdata"
      bordered
      size="small"
      :pagination="false"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'sensorNo'">
          {{ record.prefix + record.sensorNo }}
        </template>
        <template v-if="column.key === 'currentTemperature'">
          {{ record.currentTemperature }}℃
        </template>
        <template v-if="column.key === 'currentHumidity'">
          {{ record.currentHumidity }}%
        </template>
        <template v-if="column.key === 'currentDewPoint'">
          {{ record.currentDewPoint }}℃
        </template>
        <template v-if="column.key === 'thermoelectricValve'">
          <div style="display: flex; flex-direction: row">
            <div
              style="
                width: 150px;
                margin-left: 10px;
                border: 1px dashed #ccc;
                padding: 4px;
              "
              v-for="(item, index) in record.thermoelectricValve"
            >
              <a-row>
                <a-col :span="12">Y_{{ item.thermoelectricValveDO }}</a-col>
                <a-col :span="12">
                  <a-space>
                    <a-button
                      type="primary"
                      size="small"
                      @click="handleStatus(4, item, true)"
                      >开</a-button
                    >
                    <a-button
                      type="primary"
                      size="small"
                      @click="handleStatus(4, item, false)"
                      >关</a-button
                    >
                  </a-space>
                </a-col>
              </a-row>
            </div>
          </div>
        </template>
        <template v-if="column.key === 'airValve'">
          <div style="display: flex; flex-direction: row">
            <div
              style="
                width: 150px;
                margin-left: 10px;
                border: 1px dashed #ccc;
                padding: 4px;
              "
              v-for="(item, index) in record.airValve"
            >
              <a-row>
                <a-col :span="12">Y_{{ item.airValveDO }}</a-col>
                <a-col :span="12">
                  <a-space>
                    <a-button
                      type="primary"
                      size="small"
                      @click="handleStatus(5, item, true)"
                      >开</a-button
                    >
                    <a-button
                      type="primary"
                      size="small"
                      @click="handleStatus(5, item, false)"
                      >关</a-button
                    >
                  </a-space>
                </a-col>
              </a-row>
            </div>
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>
<script setup>
import { ref, reactive, watch, withMemo, onMounted, onBeforeUnmount } from 'vue'
import pcport from '@/api/methods/pcport'
import { Modal, message } from 'ant-design-vue'
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const columns1 = [
  {
    title: '分区',
    key: 'zoneNo',
  },
  {
    title: '名称',
    dataIndex: 'zoneName',
  },
  {
    title: '水泵上报状态',
    key: 'pumpStatus',
  },
  {
    title: '水泵端口',
    key: 'waterPumpDO',
    width: 300,
  },
  {
    title: '供水水温',
    key: 'waterSupplyTemperature',
  },
  {
    title: '回水水温',
    key: 'returnWaterTemperature',
  },
  {
    title: '分配房间',
    key: 'sensorConfigName',
  },
]
const columns2 = [
  {
    title: '分区',
    key: 'zoneNo',
  },
  {
    title: '名称',
    dataIndex: 'freshAirFanName',
  },
  // {
  //   title: '新风机地址',
  //   dataIndex: 'freshAirFanAdress',
  // },
  // {
  //   title: 'Modbus接口',
  //   dataIndex: 'modbus',
  // },
  {
    title: '电源上报状态',
    key: 'freshAirFanStatus',
  },
  {
    title: '新风机电源端口',
    key: 'freshAirFanPowerSupplyDO',
  },
  {
    title: '高温泵上报状态',
    key: 'getHighTemperaturePumpStatus',
  },
  {
    title: '高温泵端口',
    key: 'highTemperaturePumpDO',
  },

  {
    title: '送风温度',
    key: 'supplyAirTemperature',
  },
  {
    title: '室外温度',
    key: 'outdoorTemperature',
  },
  {
    title: '分配房间',
    key: 'sensorConfigName',
  },
]
const columns3 = [
  {
    title: '房间名称',
    dataIndex: 'roomName',
  },
  {
    title: '传感器端口',
    key: 'sensorNo',
  },
  {
    title: '实际温度',
    key: 'currentTemperature',
  },
  {
    title: '实际湿度',
    key: 'currentHumidity',
  },
  {
    title: '实际露点',
    key: 'currentDewPoint',
  },
  {
    title: '电磁阀',
    key: 'thermoelectricValve',
  },
  {
    title: '风阀',
    key: 'airValve',
  },
]
const radiationZonedata = ref([])
const freshAirFandata = ref([])
const sensorConfigdata = ref([])
const init = () => {
  pcport.createTest({ projectId: props.recordwhere.projectId }).then(res => {
    radiationZonedata.value = res.data.radiationZone
    freshAirFandata.value = res.data.freshAirFan
    sensorConfigdata.value = res.data.sensorConfig
  })
}
init()

const selectHex = () => {
  pcport.selectHex({ projectId: props.recordwhere.projectId }).then(res => {})
}
selectHex()
// 定义定时器变量
let timer = null
// 启动定时器
const startTimer = () => {
  timer = setInterval(() => {
    selectHex()
    init()
  }, 10000)
}

// 在组件挂载后启动定时器
onMounted(() => {
  startTimer()
})

// 在组件销毁前清除定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }
})

const onSave = () => {
  pcport.save({ projectId: props.recordwhere.projectId }).then(res => {
    // message.success({
    //   content: '成功',
    //   duration: 1,
    //   onClose: () => {
    //     projectProgress.value = 5
    //     activeButton.value = 6
    //     activeButtonshow.value = true
    //   },
    // })
  })
}
const handleStatus = (type, record, status) => {
  var id = ''
  switch (type) {
    case 1:
      id = record.radiationZoneValueID
      break
    case 2:
      id = record.freshAirFanValueId
      break
    case 3:
      id = record.highTemperaturePumpId
      break
    case 4:
      id = record.id
      break
    case 5:
      id = record.id
      break
  }

  var data = {
    projectId: props.recordwhere.projectId,
    type,
    id,
    status: status,
  }
  pcport.portDebugStatus(data).then(res => {
    message.success({
      content: '成功',
      duration: 1,
      onClose: () => {},
    })
  })
}
</script>
<style scoped>
.ant-card-head {
  background-color: #ffffff !important;
}
</style>
