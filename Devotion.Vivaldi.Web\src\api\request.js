import { createAlova } from 'alova'
import VueHook from 'alova/vue'
import adapterFetch from 'alova/fetch'
import { message } from 'ant-design-vue'
import { useUserStore } from '@/stores/user'
import router from '../router'
import fullScreenLoading from '@/utils/fullScreenLoading'
const serviceAlova = createAlova({
  baseURL: import.meta.env.VITE_BASE_API,
  timeout: 180000,
  cacheFor: 0, //缓存时间
  statesHook: VueHook,
  beforeRequest(method) {
    const userStore = useUserStore()
    if (userStore.token) {
      method.config.headers.authorization = 'Bearer ' + userStore.token
    } else {
      if (router.currentRoute.value.path !== '/login') {
        router.push('/login')
      }
    }
  },
  requestAdapter: adapterFetch(),
  responded: {
    // 请求成功的拦截器
    // 当使用GlobalFetch请求适配器时，第一个参数接收Response对象
    // 第二个参数为当前请求的method实例，你可以用它同步请求前后的配置信息
    onSuccess: async (response, method) => {
      message.destroy() // 请求成功后销毁加载提示
      fullScreenLoading.hide()
      if (response.status == 200) {
        const json = await response.json()
        switch (json.code) {
          case 200:
            return Promise.resolve(json)
          case 201:
            message.error(json.msg)
            break
          case 202:
            message.error(json.errors)
            break
          case 401:
            message.error({
              content: json.errors + '，请重新登录',
              duration: 1,
              onClose: () => {
                router.push('/login')
              },
            })
            break
          case 500:
            message.error(json.errors)
            break
        }
        // 解析的响应数据将传给method实例的transformData钩子函数，这些函数将在后续讲解
        return Promise.reject(json)
      } else {
        message.error(response.status)
        return Promise.reject(response.status)
      }
    },
    // 请求失败的拦截器
    // 请求错误时将会进入该拦截器。
    // 第二个参数为当前请求的method实例，你可以用它同步请求前后的配置信息
    onError: (error, method) => {
      message.destroy() // 请求成功后销毁加载提示
      fullScreenLoading.hide()
      message.error(error.message)
      return Promise.reject(error.message)
    },
    // 请求完成的拦截器
    // 当你需要在请求不论是成功、失败、还是命中缓存都需要执行的逻辑时，可以在创建alova实例时指定全局的`onComplete`拦截器，例如关闭请求 loading 状态。
    // 接收当前请求的method实例
    onComplete: () => {
      // 处理请求完成逻辑
    },
  },
})
// 封装 GET 请求
export const get = (
  url,
  params,
  showLoading = false,
  loadingText = '请求中...',
) => {
  return new Promise((resolve, reject) => {
    if (showLoading) {
      // message.loading(loadingText, 0) // 显示加载提示

      // 显示全屏加载
      fullScreenLoading.show(loadingText)
    }

    serviceAlova
      .Get(url, { params })
      .then(result => {
        resolve(result)
      }) // 请求成功，解析数据
      .catch(error => {
        reject(error) // 请求失败，抛出错误
      })
  })
}
// 封装 POST 请求
export const post = (
  url,
  data = {},
  showLoading = false,
  loadingText = '请求中...',
) => {
  return new Promise((resolve, reject) => {
    if (showLoading) {
      // message.loading(loadingText, 0) // 显示加载提示

      // 显示全屏加载
      fullScreenLoading.show(loadingText)
    }
    serviceAlova
      .Post(url, data)
      .then(result => {
        resolve(result)
      }) // 请求成功，解析数据
      .catch(error => {
        reject(error) // 请求失败，抛出错误
      })
  })
}
