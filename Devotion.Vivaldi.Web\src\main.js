import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPersist from 'pinia-plugin-persist'
import Antd from 'ant-design-vue'
import App from './App.vue'
import router from './router'
import './assets/css/style.css'
import './assets/css/zcui.css'
import sysMenu from '@/api/methods/sysMenu'
import * as Icons from '@ant-design/icons-vue'
import home from './views/home.vue'
import system from '@/api/methods/system'
// 引入 Ant Design Vue 的语言包

const app = createApp(App)
// 注册所有图标为全局组件
Object.keys(Icons).forEach(key => {
  app.component(key, Icons[key])
})

// 将图标注册为全局变量
app.config.globalProperties.$icons = Icons
const pinia = createPinia()
// 配置 Pinia 持久化插件
pinia.use(piniaPersist)
// 使用 Pinia
app.use(pinia)
//使用 Ant Design
app.use(Antd)

// 动态导入所有视图组件
const modules = import.meta.glob('./views/**/*.vue')
// 异步初始化路由和挂载应用
async function init() {
  try {
    const res = await sysMenu.getRoutesMenu()
    const dynamicRoutes = res.data

    dynamicRoutes.forEach(x => {
      if (x.type === 1) {
        const addRoutes = (parentRoute, children) => {
          if (children && children.length > 0) {
            parentRoute.children = children.map(child => {
              const childRoute = {
                path: child.path,
                meta: { title: child.title },
                component: modules[`./views${child.path}.vue`],
                meta: { cache: child.cache },
              }
              // 递归处理多级子路由
              if (child.children) {
                addRoutes(childRoute, child.children)
              }
              return childRoute
            })
          }
        }
        const parentRoute = {
          path: '/',
          component: home,
        }

        addRoutes(parentRoute, x.children)
        // 动态添加路由
        router.addRoute(parentRoute)
      } else {
        const parentRoute = {
          path: '/',
          component: home,
          children: [
            {
              path: x.path,
              meta: { title: x.title },
              component: modules[`./views${x.path}.vue`],
              meta: { cache: x.cache },
            },
          ],
        }
        // 动态添加路由
        router.addRoute(parentRoute)
      }
    })

    // 使用路由
    app.use(router)

    // 挂载应用
    app.mount('#app')
  } catch (error) {
    console.error('初始化路由失败:', error)
  }
}
const init1 = () => {
  system.deviceOnline().then(res => {})
}
init()
