import { message } from 'ant-design-vue';

const defaultDuration = 3; // 默认展示时间为 3 秒

const Message = {
  // 成功消息
  success(content, duration = defaultDuration, onClose) {
    return message.success({
      content,
      duration,
      onClose,
    });
  },

  // 错误消息
  error(content, duration = defaultDuration, onClose) {
    return message.error({
      content,
      duration,
      onClose,
    });
  },

  // 警告消息
  warning(content, duration = defaultDuration, onClose) {
    return message.warning({
      content,
      duration,
      onClose,
    });
  },

  // 信息消息
  info(content, duration = defaultDuration, onClose) {
    return message.info({
      content,
      duration,
      onClose,
    });
  },

  // 加载消息
  loading(content, duration = defaultDuration, onClose) {
    return message.loading({
      content,
      duration,
      onClose,
    });
  },

  // 自定义消息
  custom({ type, content, duration = defaultDuration, onClose }) {
    return message[type]({
      content,
      duration,
      onClose,
    });
  },
};

export default Message;
