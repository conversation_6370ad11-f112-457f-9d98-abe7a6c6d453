<template>
  <a-tabs class="" v-model:activeKey="activeKey" @change="activeKeyChange">
    <a-tab-pane key="1" tab="已占端口信息">
      <a-row :gutter="16">
        <a-col class="gutter-row" :span="12">
          <table>
            <tr>
              <th>DO端口</th>
              <th>继电器</th>
              <th>名称</th>
              <th>所属分区</th>
              <th>所属房间</th>
            </tr>
            <tr v-for="(item, index) in portdata.doPort" :key="index">
              <td>{{ item.port }}</td>
              <td>{{ item.sensorName }}</td>
              <td>{{ item.portName }}</td>
              <td>{{ item.zoneName }}</td>
              <td>
                <span class="text-color-primary">
                  {{ item.roomName }}
                </span>
              </td>
            </tr>
          </table>
        </a-col>
        <a-col class="gutter-row" :span="12">
          <table>
            <tr>
              <th>供水水温端口</th>
              <th>回水水温端口</th>
              <th>混水阀端口</th>
              <th>接收混水阀信息端口</th>
              <th>所属分区</th>
            </tr>
            <tr v-for="(item, index) in portdata.pT1000Port" :key="index">
              <td>{{ item.port }}</td>
              <td>{{ item.port }}</td>
              <td>{{ item.port }}</td>
              <td>{{ item.port }}</td>
              <td>{{ item.zoneName }}</td>
            </tr>
          </table>
        </a-col>
      </a-row>
    </a-tab-pane>
    <a-tab-pane key="2" tab="释放端口" force-render>
      <div class="bg-color-white padding-20">
        <a-form layout="inline" :model="formState" @finish="onFinish">
          <a-form-item label="传感器类别">
            <a-select
              v-model:value="formState.sensorType"
              placeholder="请选择"
              :options="sensorTypedata"
              style="width: 200px"
            >
            </a-select>
          </a-form-item>
          <a-form-item label="端口号">
            <a-input-number
              v-model:value="formState.portNumber"
              placeholder="请输入"
              style="width: 100%"
              :min="0"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" html-type="submit">查询</a-button>
              <a-button @click="resetFormState">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>
      <a-table
        :dataSource="dataSource"
        :columns="columns"
        :pagination="false"
        size="small"
        @resizeColumn="handleResizeColumn"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'sensorType'">
            <span
              v-html="
                dictTemplate.tabletempInt('sensorType', record.sensorType)
              "
            ></span>
          </template>
          <template v-if="column.key === 'roomName'">
            <span>{{ record.roomName ? record.roomName : '/' }}</span>
          </template>
          <template v-if="column.key === 'zoneName'">
            <span>{{ record.zoneName ? record.zoneName : '/' }}</span>
          </template>
          <template v-if="column.key === 'operation'">
            <a-button
              v-if="record.moduleType != 0"
              type="link"
              size="small"
              @click="releasePort(record)"
              >释放端口</a-button
            >
            <span v-if="record.moduleType == 0">无隶属关系</span>
            <a-button
              v-if="record.moduleType == 0"
              type="link"
              danger
              size="small"
              @click="tabelDelete(record)"
              >删除</a-button
            >
          </template>
        </template>
      </a-table>
    </a-tab-pane>
  </a-tabs>
</template>
<script setup>
import { ref, defineProps, reactive, nextTick, onMounted } from 'vue'
import dictTemplate from '@/utils/dictTemplate'
import { dictStore } from '@/stores/dict'
import pcport from '@/api/methods/pcport'
import { Modal, message } from 'ant-design-vue'
const dict = dictStore()
var sensorTypedata = dict.getDictItems('sensorType')
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const portdata = reactive({
  doPort: [],
  pT1000Port: [],
  mixingValveort: [],
  mixingValveInfoPort: [],
})
const formState = reactive({
  projectId: props.recordwhere.projectId,
  sensorType: null,
  portNumber: '',
})
const columns = ref([
  {
    title: '端口号',
    dataIndex: 'portNumber',
    key: 'portNumber',
    resizable: true,
    width: 150,
  },
  {
    title: '名称',
    dataIndex: 'portName',
    key: 'portName',
    width: 300,
  },
  {
    title: '传感器类别',
    dataIndex: 'sensorType',
    key: 'sensorType',
    width: 300,
  },
  {
    title: '隶属房间',
    dataIndex: 'roomName',
    key: 'roomName',
    width: 300,
  },
  {
    title: '隶属分区',
    key: 'zoneName',
    dataIndex: 'zoneName',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const resetFormState = () => {
  formState.sensorType = null
  formState.portNumber = ''
}
const handleResizeColumn = (w, col) => {
  col.width = w
}
const dataSource = ref([])
const getPCPortbyProjectId = () => {
  pcport
    .getPCPortbyProjectId({
      projectId: props.recordwhere.projectId,
    })
    .then(res => {
      portdata.doPort = res.data.doPort
      portdata.pT1000Port = res.data.pT1000Port
      portdata.mixingValveort = res.data.mixingValveort
      portdata.mixingValveInfoPort = res.data.mixingValveInfoPort
      //calculatePortCount(portdata.doPort)
    })
}
const pcPortList = () => {
  pcport.pcPortList(formState).then(res => {
    dataSource.value = res.data
  })
}
getPCPortbyProjectId()
const activeKeyChange = e => {
  nextTick(() => {
    if (e == 1) {
      getPCPortbyProjectId()
    }
    if (e == 2) {
      pcPortList()
    }
  })
}
const onFinish = () => {
  pcPortList()
}

// 获取 rowspan 值
const getRowSpan = port => portCountMap[port] || 1
// 判断是否显示 port 单元格
const showPortCell = index => {
  const currentPort = portdata.doPort[index].port
  const previousPort = portdata.doPort[index - 1]?.port
  return currentPort !== previousPort
}
//释放端口
const releasePort = record => {
  Modal.confirm({
    title: '提示',
    content: '确定要释放该端口吗？',
    onOk() {
      pcport.releasePort({ id: record.id }).then(res => {
        message.success({
          content: '成功',
          duration: 1,
          onClose: () => {
            pcPortList()
          },
        })
      })
    },
  })
}
const tabelDelete = record => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除该数据吗？',
    onOk() {
      pcport.deletePort({ id: record.id }).then(res => {
        message.success({
          content: '成功',
          duration: 1,
          onClose: () => {
            pcPortList()
          },
        })
      })
    },
  })
}
</script>
<style scoped>
table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  table-layout: fixed; /* 固定布局，确保宽度生效 */
}
th,
td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
  word-wrap: break-word; /* 避免超出内容影响宽度 */
}
th {
  background-color: #f2f2f2;
}
th:first-child,
td:first-child {
  width: 200px; /* 第一列的宽度设置为200像素 */
}
</style>
