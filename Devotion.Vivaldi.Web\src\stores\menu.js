import { defineStore } from 'pinia'
import sysMenu from '@/api/methods/sysMenu'

export const menuStore = defineStore({
  id: 'menu',
  state: () => {
    return {
      openmenuKey: [],
      selectmenuKey: [],
      tagmenus: [
        {
          key: 0,
          parentId: '0',
          title: `首页`,
          closable: false,
          path: '/console',
        },
      ],
      menus: [],
    }
  },
  actions: {
    loadmenu() {
      sysMenu
        .getmenu()
        .then(res => {
          this.menus = res.data
        })
        .catch()
    },
    setSelectmenuKey(keys) {
      this.selectmenuKey = keys
    },
    setOpenmenuKey(keys) {
      this.openmenuKey = keys
    },
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'menu',
        storage: localStorage,
        paths: ['menus', 'openmenuKey', 'selectmenuKey', 'tagmenus'],
      },
    ],
  },
})
