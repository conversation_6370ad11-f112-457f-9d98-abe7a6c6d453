import { get, post } from '@/api/request'
const quartz = {
  //根据id获取信息
  get(params) {
    return get('quartz/get', params)
  },
  //添加任务
  addTask(params) {
    return post('quartz/addTask', params)
  },
  //修改
  update(params) {
    return post('quartz/update', params)
  },
  //暂停任务
  pauseTask(params) {
    return get('quartz/pauseTask', params)
  },
  //恢复任务
  resumeTask(params) {
    return get('quartz/resumeTask', params)
  },
  //删除任务
  deleteTask(params) {
    return get('quartz/deleteTask', params)
  },
  //立即执行
  triggerTask(params) {
    return get('quartz/triggerTask', params)
  },
  //获取程序集和类名
  getAssembly(params) {
    return get('quartz/getAssembly', params)
  },
}
export default quartz
