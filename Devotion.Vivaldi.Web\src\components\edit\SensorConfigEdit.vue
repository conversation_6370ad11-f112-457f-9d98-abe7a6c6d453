<template>
  <a-drawer
    :title="editTitle"
    :width="540"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-form-item
        label="传感器编号（范围0-40）"
        name="sensorNo"
        :rules="[{ required: true, message: '传感器编号不能为空' }]"
      >
        <a-input-number
          style="width: 100%"
          :min="0"
          :max="40"
          addon-before="S_"
          v-model:value="formInfo.sensorNo"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        label="房间名称"
        name="roomName"
        :rules="[{ required: true, message: '房间名称不能为空' }]"
      >
        <a-input v-model:value="formInfo.roomName" placeholder="请输入" />
      </a-form-item>
      <a-form-item
        label="房间类型"
        name="roomType"
        :rules="[{ required: true, message: '请选择房间类型' }]"
      >
        <a-select
          v-model:value="formInfo.roomType"
          placeholder="请选择"
          :options="roomTypedata"
        >
        </a-select>
      </a-form-item>

      <a-form-item
        label="楼层"
        name="floor"
        :rules="[{ required: true, message: '楼层不能为空' }]"
      >
        <a-input-number
          style="width: 100%"
          :min="0"
          v-model:value="formInfo.floor"
          placeholder="请输入"
          addon-after="楼"
        />
      </a-form-item>

      <a-form-item label="房间面积" name="roomSize">
        <a-input-number
          v-model:value="formInfo.roomSize"
          placeholder="请输入"
          style="width: 100%"
          :min="0"
          :step="0.01"
          addon-after="m²"
        />
      </a-form-item>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import sensorConfig from '@/api/methods/sensorConfig'
import { dictStore } from '@/stores/dict'
const dict = dictStore()
var roomTypedata = dict.getDictItems('roomType')

const props = defineProps({
  editTitle: {
    type: String,
    default: '新增',
  },
  open: {
    type: Boolean,
    required: true,
  },
  formInfo: {
    type: Object,
    required: true,
  },
})
const formRef = ref(null)

const onSave = () => {
  formRef.value.validate().then(() => {
    if (props.formInfo.id == '') {
      sensorConfig.add(props.formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    } else {
      sensorConfig.update(props.formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    }
  })
}

// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close', 'updateData'])
const onClose = () => {
  emit('close')
}
</script>
