:root {
  --primary: #1677ff;
  --success: #52c41a;
  --warning: #faad14;
  --error: #ff4d4f;
  --white: #ffffff;
  --gray: #d9d9d9;
  --textgray: #969799;
  --black: #323233;
  --red: #f5222d;
  --volcano: #fa541c;
  --orange: #fa8c16;
  --gold: #faad14;
  --yellow: #fadb14;
  --lime: #a0d911;
  --green: #52c41a;
  --cyan: #13c2c2;
  --daybreakblue: #1677ff;
  --geekblue: #1d39c4;
  --purple: #722ed1;
  --magenta: #eb2f96;
  --shadowsize: 0px 0px 10px 4px;
}
/*==========字体风格================*/
/*大小*/
.text-font-10 {
  font-size: 10px;
}
.text-font-11 {
  font-size: 11px;
}
.text-font-12 {
  font-size: 12px;
}
.text-font-13 {
  font-size: 13px;
}
.text-font-14 {
  font-size: 14px;
}
.text-font-15 {
  font-size: 15px;
}
.text-font-16 {
  font-size: 16px;
}
.text-font-17 {
  font-size: 17px;
}
.text-font-18 {
  font-size: 18px;
}
.text-font-19 {
  font-size: 19px;
}
.text-font-20 {
  font-size: 20px;
}
.text-font-21 {
  font-size: 21px;
}
.text-font-22 {
  font-size: 22px;
}
.text-font-23 {
  font-size: 23px;
}
.text-font-24 {
  font-size: 24px;
}
.text-font-25 {
  font-size: 25px;
}
.text-font-26 {
  font-size: 26px;
}
.text-font-27 {
  font-size: 27px;
}
.text-font-28 {
  font-size: 28px;
}
.text-font-29 {
  font-size: 29px;
}
.text-font-30 {
  font-size: 30px;
}
.text-font-31 {
  font-size: 31px;
}
.text-font-32 {
  font-size: 32px;
}
.text-font-33 {
  font-size: 33px;
}
.text-font-34 {
  font-size: 34px;
}
.text-font-35 {
  font-size: 35px;
}
.text-font-36 {
  font-size: 36px;
}
.text-font-37 {
  font-size: 37px;
}
.text-font-38 {
  font-size: 38px;
}
.text-font-39 {
  font-size: 39px;
}
.text-font-40 {
  font-size: 40px;
}
/*粗细*/
.text-font-bold {
  font-weight: bold;
}
.text-font-bolder {
  font-weight: bolder;
}
.text-font-normal {
  font-weight: normal;
}
.text-font-lighter {
  font-weight: lighter;
}
/*位置*/
.text-align-center {
  text-align: center;
}
.text-align-left {
  text-align: left;
}
.text-align-right {
  text-align: right;
}
/*颜色*/
.text-color-primary {
  color: var(--primary);
}
.text-color-success {
  color: var(--success);
}
.text-color-warning {
  color: var(--warning);
}
.text-color-error {
  color: var(--error);
}
.text-color-white {
  color: var(--white);
}
.text-color-gray {
  color: var(--gray);
}
.text-color-textgray {
  color: var(--textgray);
}
.text-color-black {
  color: var(--black);
}
.text-color-red {
  color: var(--red);
}
.text-color-volcano {
  color: var(--volcano);
}
.text-color-orange {
  color: var(--orange);
}
.text-color-gold {
  color: var(--gold);
}
.text-color-yellow {
  color: var(--yellow);
}
.text-color-lime {
  color: var(--lime);
}
.text-color-green {
  color: var(--green);
}
.text-color-cyan {
  color: var(--cyan);
}
.text-color-daybreakblue {
  color: var(--daybreakblue);
}
.text-color-geekblue {
  color: var(--geekblue);
}
.text-color-purple {
  color: var(--purple);
}
.text-color-magenta {
  color: var(--magenta);
}
/*==========背景================*/
.bg-color-primary {
  background-color: var(--primary);
}
.bg-color-success {
  background-color: var(--success);
}
.bg-color-warning {
  background-color: var(--warning);
}
.bg-color-error {
  background-color: var(--error);
}
.bg-color-white {
  background-color: var(--white);
}
.bg-color-gray {
  background-color: var(--gray);
}
.bg-color-black {
  background-color: var(--black);
}
.bg-color-red {
  background-color: var(--red);
}
.bg-color-volcano {
  background-color: var(--volcano);
}
.bg-color-orange {
  background-color: var(--orange);
}
.bg-color-gold {
  background-color: var(--gold);
}
.bg-color-yellow {
  background-color: var(--yellow);
}
.bg-color-lime {
  background-color: var(--lime);
}
.bg-color-green {
  background-color: var(--green);
}
.bg-color-cyan {
  background-color: var(--cyan);
}
.bg-color-daybreakblue {
  background-color: var(--daybreakblue);
}
.bg-color-geekblue {
  background-color: var(--geekblue);
}
.bg-color-purple {
  background-color: var(--purple);
}
.bg-color-magenta {
  background-color: var(--magenta);
}
/*===============边距================*/
/*内边距*/
.padding-5 {
  padding: 5px;
}
.padding-10 {
  padding: 10px;
}
.padding-20 {
  padding: 20px;
}
.padding-30 {
  padding: 30px;
}
.padding-40 {
  padding: 40px;
}
.padding-50 {
  padding: 50px;
}
.padding-left-5 {
  padding-left: 5px;
}
.padding-left-10 {
  padding-left: 10px;
}
.padding-left-20 {
  padding-left: 20px;
}
.padding-left-30 {
  padding-left: 30px;
}
.padding-left-40 {
  padding-left: 40px;
}
.padding-left-50 {
  padding-left: 50px;
}
.padding-right-2 {
  padding-right: 2px;
}
.padding-right-5 {
  padding-right: 5px;
}
.padding-right-10 {
  padding-right: 10px;
}
.padding-right-20 {
  padding-right: 20px;
}
.padding-right-30 {
  padding-right: 30px;
}
.padding-right-40 {
  padding-right: 40px;
}
.padding-right-50 {
  padding-right: 50px;
}
.padding-top-5 {
  padding-top: 5px;
}
.padding-top-10 {
  padding-top: 10px;
}
.padding-top-20 {
  padding-top: 20px;
}
.padding-top-30 {
  padding-top: 30px;
}
.padding-top-40 {
  padding-top: 40px;
}
.padding-top-50 {
  padding-top: 50px;
}
.padding-bottom-5 {
  padding-bottom: 5px;
}
.padding-bottom-10 {
  padding-bottom: 10px;
}
.padding-bottom-20 {
  padding-bottom: 20px;
}
.padding-bottom-30 {
  padding-bottom: 30px;
}
.padding-bottom-40 {
  padding-bottom: 40px;
}
.padding-bottom-50 {
  padding-bottom: 50px;
}
.padding-lr-5 {
  padding-left: 5px;
  padding-right: 5px;
}
.padding-lr-10 {
  padding-left: 10px;
  padding-right: 10px;
}
.padding-lr-20 {
  padding-left: 20px;
  padding-right: 20px;
}
.padding-lr-30 {
  padding-left: 30px;
  padding-right: 30px;
}
.padding-lr-40 {
  padding-left: 40px;
  padding-right: 40px;
}
.padding-lr-50 {
  padding-left: 50px;
  padding-right: 50px;
}
.padding-tb-5 {
  padding-top: 5px;
  padding-bottom: 5px;
}
.padding-tb-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}
.padding-tb-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}
.padding-tb-30 {
  padding-top: 30px;
  padding-bottom: 30px;
}
.padding-tb-40 {
  padding-top: 40px;
  padding-bottom: 40px;
}
.padding-tb-50 {
  padding-top: 50px;
  padding-bottom: 50px;
}
/*外边距*/
.margin-5 {
  margin: 5px;
}
.margin-10 {
  margin: 10px;
}
.margin-20 {
  margin: 20px;
}
.margin-30 {
  margin: 30px;
}
.margin-40 {
  margin: 40px;
}
.margin-50 {
  margin: 50px;
}
.margin-left-5 {
  margin-left: 5px;
}
.margin-left-10 {
  margin-left: 10px;
}
.margin-left-20 {
  margin-left: 20px;
}
.margin-left-30 {
  margin-left: 30px;
}
.margin-left-40 {
  margin-left: 40px;
}
.margin-left-50 {
  margin-left: 50px;
}
.margin-right-5 {
  margin-right: 5px;
}
.margin-right-10 {
  margin-right: 10px;
}
.margin-right-20 {
  margin-right: 20px;
}
.margin-right-30 {
  margin-right: 30px;
}
.margin-right-40 {
  margin-right: 40px;
}
.margin-right-50 {
  margin-right: 50px;
}
.margin-top-5 {
  margin-top: 5px;
}
.margin-top-10 {
  margin-top: 10px;
}
.margin-top-20 {
  margin-top: 20px;
}
.margin-top-30 {
  margin-top: 30px;
}
.margin-top-40 {
  margin-top: 40px;
}
.margin-top-50 {
  margin-top: 50px;
}
.margin-bottom-5 {
  margin-bottom: 5px;
}
.margin-bottom-10 {
  margin-bottom: 10px;
}
.margin-bottom-15 {
  margin-bottom: 15px;
}
.margin-bottom-20 {
  margin-bottom: 20px;
}
.margin-bottom-30 {
  margin-bottom: 30px;
}
.margin-bottom-40 {
  margin-bottom: 40px;
}
.margin-bottom-50 {
  margin-bottom: 50px;
}
.margin-lr-5 {
  margin-left: 5px;
  margin-right: 5px;
}
.margin-lr-10 {
  margin-left: 10px;
  margin-right: 10px;
}
.margin-lr-20 {
  margin-left: 20px;
  margin-right: 20px;
}
.margin-lr-30 {
  margin-left: 30px;
  margin-right: 30px;
}
.margin-lr-40 {
  margin-left: 40px;
  margin-right: 40px;
}
.margin-lr-50 {
  margin-left: 50px;
  margin-right: 50px;
}
.margin-tb-5 {
  margin-top: 5px;
  margin-bottom: 5px;
}
.margin-tb-10 {
  margin-top: 10px;
  margin-bottom: 10px;
}
.margin-tb-15 {
  margin-top: 15px;
  margin-bottom: 15px;
}
.margin-tb-20 {
  margin-top: 20px;
  margin-bottom: 20px;
}
.margin-tb-30 {
  margin-top: 30px;
  margin-bottom: 30px;
}
.margin-tb-40 {
  margin-top: 40px;
  margin-bottom: 40px;
}
.margin-tb-50 {
  margin-top: 50px;
  margin-bottom: 50px;
}
/*===============边框================*/
/* -- 实线 -- */
.border-solid {
  border: 1px solid var(--black);
}
.border-solid-top {
  border-top: 1px solid var(--black);
}
.border-solid-bottom {
  border-bottom: 1px solid var(--black);
}
.border-solid-left {
  border-left: 1px solid var(--black);
}
.border-solid-right {
  border-right: 1px solid var(--black);
}
/* -- 虚线 -- */
.border-dashed {
  border: 1px dashed var(--black);
}
.border-dashed-top {
  border-top: 1px dashed var(--black);
}
.border-dashed-bottom {
  border-bottom: 1px dashed var(--black);
}
.border-dashed-left {
  border-left: 1px dashed var(--black);
}
.border-dashed-right {
  border-right: 1px dashed var(--black);
}
/* -- 阴影 -- */
.shadow {
  box-shadow: var(--shadowsize) #cfcfcf;
}
.shadow-blur[class*='-white'] {
  box-shadow: var(--shadowsize) var(--white);
}
.shadow-blur[class*='-gray'] {
  box-shadow: var(--shadowsize) var(--gray);
}
.shadow-blur[class*='-black'] {
  box-shadow: var(--shadowsize) var(--black);
}
.shadow-blur[class*='-volcano'] {
  box-shadow: var(--shadowsize) var(--volcano);
}
.shadow-blur[class*='-orange'] {
  box-shadow: var(--shadowsize) var(--orange);
}
.shadow-blur[class*='-gold'] {
  box-shadow: var(--shadowsize) var(--gold);
}
.shadow-blur[class*='-yellow'] {
  box-shadow: var(--shadowsize) var(--yellow);
}
.shadow-blur[class*='-lime'] {
  box-shadow: var(--shadowsize) var(--lime);
}
.shadow-blur[class*='-green'] {
  box-shadow: var(--shadowsize) var(--green);
}
.shadow-blur[class*='-cyan'] {
  box-shadow: var(--shadowsize) var(--cyan);
}
.shadow-blur[class*='-daybreakblue'] {
  box-shadow: var(--shadowsize) var(--daybreakblue);
}
.shadow-blur[class*='-geekblue'] {
  box-shadow: var(--shadowsize) var(--geekblue);
}
.shadow-blur[class*='-purple'] {
  box-shadow: var(--shadowsize) var(--purple);
}
.shadow-blur[class*='-magenta'] {
  box-shadow: var(--shadowsize) var(--magenta);
}
/* -- 颜色 -- */
.border-color-white {
  border: 1px solid var(--white);
}
.border-color-gray {
  border: 1px solid var(--gray);
}
.border-color-black {
  border: 1px solid var(--black);
}
.border-color-volcano {
  border: 1px solid var(--volcano);
}
.border-color-orange {
  border: 1px solid var(--orange);
}
.border-color-gold {
  border: 1px solid var(--gold);
}
.border-color-yellow {
  border: 1px solid var(--yellow);
}
.border-color-lime {
  border: 1px solid var(--lime);
}
.border-color-green {
  border: 1px solid var(--green);
}
.border-color-cyan {
  border: 1px solid var(--cyan);
}
.border-color-daybreakblue {
  border: 1px solid var(--daybreakblue);
}
.border-color-geekblue {
  border: 1px solid var(--geekblue);
}
.border-color-purple {
  border: 1px solid var(--purple);
}
.border-color-magenta {
  border: 1px solid var(--magenta);
}
/* -- 弧度 -- */
.border-radius {
  border-radius: 8px;
}
/*===============标签================*/
.plain-span-primary {
  border: 1px solid var(--primary);
  color: var(--primary);
  padding: 4px 8px;
  font-size: 11px !important;
  border-radius: 3px;
}
.plain-span-success {
  border: 1px solid var(--success);
  color: var(--success);
  padding: 4px 8px;
  font-size: 11px !important;
  border-radius: 3px;
}
.plain-span-warning {
  border: 1px solid var(--warning);
  color: var(--warning);
  padding: 4px 8px;
  font-size: 11px !important;
  border-radius: 3px;
}
.plain-span-error {
  border: 1px solid var(--error);
  color: var(--error);
  padding: 4px 8px;
  font-size: 11px !important;
  border-radius: 3px;
}
/*===============布局================*/
/* -- flex -- */
.flex-column {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.flex-row {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.flex-item {
  flex: 1;
}
.flex-item-1 {
  flex: 0.1;
}
.flex-item-2 {
  flex: 0.2;
}
.flex-item-3 {
  flex: 0.3;
}
.flex-item-4 {
  flex: 0.4;
}
.flex-item-5 {
  flex: 0.5;
}
.flex-item-6 {
  flex: 0.6;
}
.flex-item-7 {
  flex: 0.7;
}
.flex-item-8 {
  flex: 0.8;
}
.flex-item-9 {
  flex: 0.9;
}

/*按钮*/
/*无边框按钮*/
.btn-nobor {
  cursor: pointer;
  margin: 0 3px;
}
.btn-nobor[class*='-primary']:hover {
  color: var(--primary);
  opacity: 0.6 !important;
}
.btn-nobor[class*='-success']:hover {
  color: var(--success);
  opacity: 0.6 !important;
}
.btn-nobor[class*='-warning']:hover {
  color: var(--warning);
  opacity: 0.6 !important;
}
.btn-nobor[class*='-error']:hover {
  color: var(--error);
  opacity: 0.6 !important;
}
/* 徽章 */
.zc-badge-default {
  padding: 3px 8px;
  border: 1px solid var(--textgray);
  color: var(--textgray);
  background: #e4e4e4;
  font-size: 12px !important;
  border-radius: 3px;
}
.zc-badge-success {
  padding: 3px 8px;
  border: 1px solid var(--success);
  color: var(--success);
  background: #eafff6;
  font-size: 12px !important;
  border-radius: 3px;
}
.zc-badge-primary {
  padding: 3px 8px;
  border: 1px solid var(--primary);
  color: var(--primary);
  background: #e8f3ff;
  font-size: 12px !important;
  border-radius: 3px;
}
.zc-badge-warning {
  padding: 3px 8px;
  border: 1px solid var(--warning);
  color: var(--warning);
  background: #fff5e6;
  font-size: 12px !important;
  border-radius: 3px;
}
.zc-badge-error {
  padding: 3px 8px;
  border: 1px solid var(--error);
  color: var(--error);
  background: #ffe8e2;
  font-size: 12px !important;
  border-radius: 3px;
}
.zc-badge-magenta {
  padding: 3px 8px;
  border: 1px solid var(--magenta);
  color: var(--magenta);
  background: #ffe4f7;
  font-size: 12px !important;
  border-radius: 3px;
}
/*详情*/
.detail-view {
  padding: 10px;
  color: #515a6e;
}
.detail-view > div {
  padding-bottom: 15px;
}
.detail-row {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.detail-row-title {
  width: 120px;
  text-align: right;
}
.detail-row-value {
  margin-left: 5px;
}
/*选项按钮切换*/
.select-tab {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.select-tab-btn {
  padding: 5px 15px;
  border: 1px solid #eee;
  margin: 15px 10px 15px 20px;
  border-radius: 5px;
  cursor: pointer;
  color: #969799;
}

.select-tab-btn-this {
  background-color: #1677ff;
  color: #fff;
  border: 0;
}
/* 块标题 */
.blockquote {
  border-left: 4px solid var(--primary);
  background-color: #f8f9fa;
  font-size: 1.1em;
  line-height: 1.5;
  color: #343a40;
  padding: 1em;
  font-weight: bolder;
  margin: 10px 0;
}
