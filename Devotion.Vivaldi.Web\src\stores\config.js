import { defineStore } from 'pinia'
import dayjs from 'dayjs'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import enUS from 'ant-design-vue/es/locale/en_US'
import 'dayjs/locale/zh-cn' // 中文
import 'dayjs/locale/en' // 英文
export const configStore = defineStore({
  id: 'config',
  state: () => ({
    themeClass: 'dark',
    tableSize: 'middle',
    tableBordered: false,
    locale: zhCN,
    dayjsLocale: 'zh-cn',
  }),
  actions: {
    setLocale(locale) {
      this.locale = locale
      this.dayjsLocale = locale === zhCN ? 'zh-cn' : 'en'
      dayjs.locale(this.dayjsLocale)
    },
  },
  persist: {
    enabled: true,
    strategies: [
      {
        key: 'config',
        storage: localStorage,
        paths: ['themeClass', 'tableSize', 'tableBordered', 'locale'],
      },
    ],
  },
})
