<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="project"
    pageAction="RunPage"
    :rowSelect="false"
    ref="childRef"
  >
    <template #custom-projectAddress="{ record }">
      {{
        record.province + record.city + record.district + record.detailedAddress
      }}
    </template>
    <template #custom-online="{ record }">
      <span v-if="record.online">
        <a-badge status="processing" color="#52c41a" />
        <span class="text-color-success">在线</span>
      </span>
      <span v-else>
        <a-badge status="error" />
        <span class="text-color-error">离线</span>
      </span>
    </template>
    <template #custom-operation="{ record }">
      <a-button type="link" size="small" @click="setting(record)"
        >运行设置</a-button
      >
      <a-button type="link" size="small" @click="statistics(record)"
        >统计分析</a-button
      >
    </template>
  </form-table>
  <a-modal
    v-model:open="settingMainOpen"
    :title="settingMaintitle"
    width="90%"
    wrap-class-name="full-modal1"
    style="top: 5%"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <SettingMain :recordwhere="recordwhere"></SettingMain>
    <template #footer>
      <!-- <a-button key="submit" type="primary" @click="settingMainOpen = false"
        >关闭</a-button
      > -->
    </template>
  </a-modal>
  <a-modal
    v-model:open="statisticsOpen"
    :title="statisticstitle"
    width="90%"
    wrap-class-name="full-modal1"
    style="top: 5%"
    :maskClosable="false"
    :destroyOnClose="true"
  >
    <StatisticsPage :recordwhere="recordwhere"></StatisticsPage>
    <template #footer>
      <!-- <a-button key="submit" type="primary" @click="settingMainOpen = false"
        >关闭</a-button
      > -->
    </template>
  </a-modal>
</template>
<script setup>
import { ref, reactive } from 'vue'
import FormTable from '../../components/FormTable.vue'
import SettingMain from './settingMain.vue'
import StatisticsPage from '../../components/pages/StatisticsPage.vue'
import router from '@/router'
const formState = ref({
  projectName: { label: '项目名称', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '网络状态',
    key: 'online',
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
  },
  {
    title: '建筑面积',
    dataIndex: 'totalArea',
    key: 'totalArea',
  },
  {
    title: '隶属小区',
    dataIndex: 'communityName',
    key: 'communityName',
  },
  {
    title: '项目地址',
    dataIndex: 'projectAddress',
    key: 'projectAddress',
  },

  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)
//编辑

const settingMainOpen = ref(false)
const settingMaintitle = ref('')
const recordwhere = ref({})
const refreshData = () => {
  childRef.value.tableLoad()
}
//设置
const setting = record => {
  settingMaintitle.value = record.projectName + '项目设置'
  settingMainOpen.value = true
  recordwhere.value = {
    projectId: record.id,
  }
}
const statisticsOpen = ref(false)
const statisticstitle = ref('统计')
//设置
const statistics = record => {
  statisticstitle.value = record.projectName + '项目的统计数据'
  statisticsOpen.value = true
  recordwhere.value = {
    projectId: record.id,
  }
}
</script>
<style>
.full-modal1 {
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(90vh);
    overflow-y: auto;
  }
  .ant-modal-body {
    flex: 1;
  }
}
</style>
