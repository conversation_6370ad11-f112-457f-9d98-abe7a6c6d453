<template>
  <a-drawer
    :title="editTitle"
    :width="540"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical" :rules="rules">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            label="任务名称"
            name="name"
            :rules="[{ required: true, message: '任务名称不能为空' }]"
          >
            <a-input v-model:value="formInfo.name" placeholder="请输入" />
          </a-form-item>
          <a-form-item
            label="触发器名称"
            name="triggerName"
            :rules="[{ required: true, message: '触发器名称不能为空' }]"
          >
            <a-input
              v-model:value="formInfo.triggerName"
              placeholder="请输入"
            />
          </a-form-item>

          <a-form-item label="任务类型" name="taskType">
            <a-select v-model:value="formInfo.taskType" placeholder="请选择">
              <a-select-option value="0">DLL</a-select-option>
              <a-select-option value="1">API</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="任务分组"
            name="jobGroup"
            :rules="[{ required: true, message: '任务分组不能为空' }]"
          >
            <a-input v-model:value="formInfo.jobGroup" placeholder="请输入" />
          </a-form-item>
          <a-form-item
            label="运行时间表达式Cron"
            name="cron"
            :rules="[{ required: true, message: 'cron不能为空' }]"
          >
            <a-input v-model:value="formInfo.cron" placeholder="请输入" />
          </a-form-item>
          <a target="_blank" href="https://cron.ciding.cc/">在线表达式Cron</a>
        </a-col>
      </a-row>
      <a-form-item
        v-if="formInfo.taskType == 0"
        label="程序集名称"
        name="assemblyName"
      >
        <a-input
          v-model:value="formInfo.assemblyName"
          disabled="true"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        v-if="formInfo.taskType == 0"
        label="任务类名"
        name="className"
      >
        <a-select
          v-model:value="formInfo.className"
          placeholder="请选择"
          :options="classNameList"
        >
        </a-select>
      </a-form-item>
      <a-form-item v-if="formInfo.taskType == 1" label="apiUrl" name="apiUrl">
        <a-input v-model:value="formInfo.apiUrl" placeholder="请输入" />
      </a-form-item>
      <a-form-item
        v-if="formInfo.taskType == 1"
        label="APi访问类型"
        name="apiRequestType"
      >
        <a-select v-model:value="formInfo.apiRequestType" placeholder="请选择">
          <a-select-option value="POST">POST</a-select-option>
          <a-select-option value="GET">GET</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="参数" name="apiParameter">
        <a-input v-model:value="formInfo.apiParameter" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="是否开启日志" name="isLog">
        <a-switch v-model:checked="formInfo.isLog" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formInfo.remark" />
      </a-form-item>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import quartz from '@/api/methods/quartz'
const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
})
//表单字段的默认值
const defaultformInfo = {
  id: '00000000-0000-0000-0000-000000000000',
  name: '',
  triggerName: '',
  jobGroup: '',
  cron: '',
  taskType: '0',
  assemblyName: '',
  className: null,
  apiUrl: '',
  apiRequestType: 'POST',
  apiParameter: '',
  isLog: true,
  remark: '',
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editTitle = ref('新增')
const classNameList = ref([])
const init = id => {
  quartz.getAssembly().then(res => {
    formInfo.assemblyName = res.data.assemblyName

    classNameList.value = res.data.classJobs.map(x => ({
      label: x,
      value: x,
    }))
  })

  if (id) {
    editTitle.value = '修改'
    quartz.get({ id }).then(res => {
      Object.assign(formInfo, res.data)
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
  }
}

const formRef = ref(null)
const onSave = () => {
  formRef.value.validate().then(() => {
    if (formInfo.id == '00000000-0000-0000-0000-000000000000') {
      quartz.addTask(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    } else {
      quartz.update(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    }
  })
}
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close', 'updateData'])
const onClose = () => {
  emit('close')
}
// 使用 defineExpose 暴露方法
defineExpose({
  init,
})
</script>
