<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="sysRole"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    @edit="edit"
    ref="childRef"
  >
    <!-- 可以通过插槽自定义单元格 -->
    <template #custom-isEnable="{ record }">
      <a-switch
        v-model:checked="record.isEnable"
        checked-children="是"
        un-checked-children="否"
        @change="handleSwitchChange(record)"
      />
    </template>
    <template #custom-operation="{ record }">
      <a-divider type="vertical" />
      <a @click="empower(record)">关联菜单</a>
    </template>
  </form-table>
  <!-- 新增修改 -->
  <SysRoleEdit
    :open="editopen"
    :formInfo="formInfo"
    @close="editopen = false"
    @updateData="refreshData"
    ref="editRef"
  >
  </SysRoleEdit>

  <a-modal v-model:open="empoweropen" title="关联菜单" @ok="empowerSave">
    <a-tree
      v-model:checkedKeys="checkedKeys"
      checkable
      :tree-data="roleMenutreeData"
    >
    </a-tree>
  </a-modal>
</template>
<script setup>
import { ref, reactive, inject } from 'vue'
import FormTable from '../../components/FormTable.vue'
import SysRoleEdit from '../../components/edit/SysRoleEdit.vue'
import sysRole from '@/api/methods/sysRole'
import { message } from 'ant-design-vue'
import { menuStore } from '@/stores/menu'
import sysMenu from '@/api/methods/sysMenu'
// 注入父组件的方法
const menuinit = inject('menuinit')
const formState = ref({
  name: { label: '角色名称', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
  },
  {
    title: '角色名称',
    dataIndex: 'name',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '是否启用',
    key: 'isEnable',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)

//编辑
const editopen = ref(false)
const editRef = ref(null)
const edit = record => {
  editRef.value.init(record.id)
  editopen.value = true
}
const refreshData = () => {
  childRef.value.tableLoad()
}

//修改是否启用
const handleSwitchChange = record => {
  var data = {
    id: record.id,
    isEnabled: record.isEnable,
  }
  sysRole.updateIsEnabled(data).then(() => {
    childRef.value.tableLoad()
  })
}

const empoweropen = ref(false)
const roleMenutreeData = ref([])
const checkedKeys = ref([])
const recordId = ref(0)
//关联菜单
const empower = record => {
  recordId.value = record.id
  sysRole.getRoleMenu({ roleId: record.id }).then(res => {
    roleMenutreeData.value = res.data.output
    checkedKeys.value = res.data.checkedKeys
    empoweropen.value = true
  })
}
//关联菜单保存
const empowerSave = () => {
  var data = {
    Id: recordId.value,
    Menus: checkedKeys.value,
  }
  sysRole.savePower(data).then(() => {
    const menu = menuStore()
    sysMenu
      .getmenu()
      .then(res => {
        menu.menus = res.data
        message.success('成功', 1)
        if (menuinit) {
          menuinit()
        }
        empoweropen.value = false
      })
      .catch()
  })
}
</script>

<style></style>
