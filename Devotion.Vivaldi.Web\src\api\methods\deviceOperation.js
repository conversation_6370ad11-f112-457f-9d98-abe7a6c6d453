import { post, get } from '@/api/request'

const deviceOperation = {
  //设备初始化OTA状态
  setUpgradeStatus(params) {
    return get('deviceOperation/setUpgradeStatus', params, true)
  },
  //获取设备当前OTA状态
  getUpgradeStatus(params) {
    return get('deviceOperation/getUpgradeStatus', params)
  },
  //下发准备 1A2B3C4D
  preparationIssuance(params) {
    return get('deviceOperation/preparationIssuance', params, true)
  },
  //下发数据帧
  distributeDataFrames(params) {
    return post('deviceOperation/distributeDataFrames', params, true)
  },
  //下发文件
  distributeDocuments(params) {
    return post('deviceOperation/distributeDocuments', params, true)
  },

  getDeviceDebugInfo(params) {
    return get('deviceOperation/GetDeviceDebugInfo', params)
  },

  Initialization(params) {
    return get('deviceOperation/Initialization', params)
  },
  Sendhex(params) {
    return get('deviceOperation/Sendhex', params)
  },
  RelayKz(params) {
    return get('deviceOperation/RelayKz', params, true)
  },
  ReMixingValveKz(params) {
    return get('deviceOperation/ReMixingValveKz', params, true)
  },

}
//
export default deviceOperation
