<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="project"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    :ftableDetails="true"
    @edit="edit"
    @details="details"
    ref="childRef"
  >
    <template #custom-provincecitydistrict="{ record }">
      {{ record.province + record.city + record.district }}
    </template>
    <template #custom-projectProgress="{ record }">
      <span
        v-html="
          dictTemplate.tabletempInt('projectProgress', record.projectProgress)
        "
      ></span>
    </template>
  </form-table>
  <!-- 新增修改 -->
  <ProjectEdit
    :open="editopen"
    @close="editopen = false"
    @updateData="refreshData"
    ref="editRef"
  >
  </ProjectEdit>
  <!-- 详情 -->
  <ProjectDetails :open="detailsopen" :info="info" @close="detailsopen = false">
  </ProjectDetails>
</template>
<script setup>
import { ref, reactive } from 'vue'
import FormTable from '../../components/FormTable.vue'
import ProjectEdit from '@/components/edit/ProjectEdit.vue'
import ProjectDetails from '@/components/details/ProjectDetails.vue'
import dictTemplate from '@/utils/dictTemplate'
import area from '@/utils/area'
import project from '@/api/methods/project'
const allProvince = ref(area.getAllProvince())
const allCity = ref(area.getAllCity())
const allAreas = ref(area.getAllAreas())
const formState = ref({
  projectName: { label: '项目名称', value: '', type: 'text' },
  province: {
    label: '省',
    value: null,
    type: 'select',
    data: allProvince,
  },
  city: {
    label: '市',
    value: null,
    type: 'select',
    data: allCity,
  },
  district: {
    label: '区',
    value: null,
    type: 'select',
    data: allAreas,
  },
  detailedAddress: { label: '详细地址', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
  },
  {
    title: '项目进度',
    key: 'projectProgress',
    width: 100,
  },
  {
    title: '省市区',
    key: 'provincecitydistrict',
  },
  {
    title: '详细地址',
    dataIndex: 'detailedAddress',
  },
  {
    title: '项目进度',
    dataIndex: 'projectProgress',
  },
  {
    title: '隶属小区',
    dataIndex: 'communityName',
  },
  {
    title: '业主',
    dataIndex: 'ownerName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)
//编辑
const editopen = ref(false)
const editRef = ref(null)

const edit = record => {
  editRef.value.init(record.id)
  editopen.value = true
}
const refreshData = () => {
  childRef.value.tableLoad()
}

//详情
const detailsopen = ref(false)
const info = reactive({})
const details = record => {
  project.detail({ id: record.id }).then(res => {
    Object.assign(info, res.data)
    detailsopen.value = true
  })
}
</script>
<style></style>
