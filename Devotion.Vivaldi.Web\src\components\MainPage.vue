<template>
  <div class="common-layout">
    <a-layout>
      <!-- 侧边栏部分 -->
      <a-layout-sider
        v-model:collapsed="collapsed"
        :trigger="null"
        collapsible
        :style="{
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          background: formConfig.themeClass == 'light' ? '#fff' : '',
        }"
      >
        <div class="logo">
          <span
            v-if="!collapsed"
            :class="
              formConfig.themeClass == 'light'
                ? 'text-color-primary'
                : 'text-color-white '
            "
          >
            <div class="flex-row">
              <img
                src="@/assets/imgs/favicon.png"
                alt=""
                width="30px"
                class="margin-right-10"
              />VIVALDI
            </div>
          </span>
          <span
            v-else
            :class="
              formConfig.themeClass == 'light'
                ? 'text-color-primary'
                : 'text-color-white '
            "
            ><img src="@/assets/imgs/favicon.png" alt="" width="30px"
          /></span>
        </div>
        <a-menu
          v-model:openKeys="openmenuKey"
          v-model:selectedKeys="selectmenuKey"
          mode="inline"
          :theme="formConfig.themeClass"
          :inline-collapsed="collapsed"
          :items="menuItems"
          @click="menuclick"
        ></a-menu>
      </a-layout-sider>
      <!-- 主体部分 -->
      <a-layout :style="{ marginLeft: collapsed ? '80px' : '200px' }">
        <a-layout-header
          class="zc-layout-header"
          :style="{
            position: 'fixed',
            zIndex: 1,
            width: collapsed ? 'calc(100% - 80px)' : 'calc(100% - 200px)',
          }"
        >
          <div class="flex-row">
            <div class="flex-item text-align-left">
              <menu-unfold-outlined
                v-if="collapsed"
                class="trigger"
                @click="() => (collapsed = !collapsed)"
              />
              <menu-fold-outlined
                v-else
                class="trigger"
                @click="() => (collapsed = !collapsed)"
              />
              <a class="text-color-black" @click="reload">
                <UndoOutlined :style="{ fontSize: '18px' }" />
              </a>
            </div>
            <div class="flex-item text-align-right padding-right-20">
              <a-space :size="18">
                <a-dropdown>
                  <a class="text-color-black" @click.prevent>
                    <a-avatar
                      :size="35"
                      :style="{
                        backgroundColor: '#1677ff',
                        verticalAlign: 'middle',
                        margin: '0 0 5px 0',
                      }"
                    >
                      {{ username }}
                    </a-avatar>
                  </a>
                  <template #overlay>
                    <a-menu @click="avatardropdownonClick">
                      <!-- <a-menu-item key="1"> 个人中心 </a-menu-item> -->
                      <a-menu-item key="2"> 退出 </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                <a
                  class="text-color-black"
                  @click.prevent
                  @click="toggleFullScreen"
                >
                  <a-tooltip :title="isFullScreen ? '退出全屏' : '进入全屏'">
                    <FullscreenOutlined v-if="isFullScreen" />
                    <FullscreenExitOutlined v-else />
                  </a-tooltip>
                </a>
                <a-dropdown>
                  <a class="text-color-black" @click.prevent>
                    <GlobalOutlined />
                  </a>
                  <template #overlay>
                    <a-menu @click="dropdownonLanguageClick">
                      <a-menu-item key="zhCN">
                        <span
                          :class="
                            config.locale.locale == 'zh-cn'
                              ? 'text-color-primary'
                              : ''
                          "
                          >简体中文</span
                        >
                      </a-menu-item>
                      <a-menu-item key="enUS">
                        <span
                          :class="
                            config.locale.locale == 'en'
                              ? 'text-color-primary'
                              : ''
                          "
                          >English</span
                        >
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                <a class="text-color-black" @click.prevent @click="showDrawer">
                  <MoreOutlined />
                </a>
              </a-space>
            </div>
          </div>
        </a-layout-header>
        <!-- 选项卡组件 -->
        <div class="zc-layout-content-tabs">
          <div
            class="zc-layout-content-tabs-left"
            @wheel.prevent="onWheelScroll"
            ref="scrollContainer"
          >
            <a-tag
              v-for="pane in panes"
              :key="pane.key"
              :class="selectmenuKey[0] == pane.key ? 'tag-select' : 'tag'"
              :closable="pane.closable"
              :bordered="false"
              @close="tagdelete(pane)"
              @click="tagchange(pane)"
            >
              {{ pane.title }}
            </a-tag>
          </div>
          <div class="zc-layout-content-tabs-right">
            <a-dropdown class="zc-layout-content-tabs-right-dropdown">
              <a class="ant-dropdown-link" @click.prevent>
                <DownOutlined />
              </a>
              <template #overlay>
                <a-menu @click="dropdownonClick">
                  <a-menu-item key="1">
                    <ArrowLeftOutlined /> 关闭左侧
                  </a-menu-item>
                  <a-menu-item key="2">
                    <ArrowRightOutlined /> 关闭右侧
                  </a-menu-item>
                  <a-menu-item key="3">
                    <CloseOutlined /> 关闭其它
                  </a-menu-item>
                  <a-menu-item key="4">
                    <CloseCircleOutlined /> 全部关闭
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
        <!-- 路由视图部分 -->
        <a-layout-content :style="{ margin: '8px 16px 0' }">
          <router-view v-slot="{ Component, route }">
            <keep-alive v-if="route.meta.cache">
              <component :is="Component" />
            </keep-alive>
            <component v-else :is="Component" />
          </router-view>
        </a-layout-content>
      </a-layout>
    </a-layout>
    <!-- 抽屉组件 -->
    <a-drawer title="配置" :closable="false" :open="open" @close="onClose">
      <a-form name="config_form" :model="formConfig">
        <div class="blockquote">主题</div>
        <div class="padding-10">
          <a-radio-group
            v-model:value="formConfig.themeClass"
            @change="themeClasschangeTheme"
          >
            <a-radio value="dark">过去黑</a-radio>
            <a-radio value="light">未来白</a-radio>
          </a-radio-group>
        </div>
        <div class="blockquote">表格设置</div>
        <div class="padding-10">
          <a-row>
            <a-col :span="12">表格边框</a-col>
            <a-col :span="12">
              <a-switch
                v-model:checked="formConfig.tableBordered"
                checked-children="边框开"
                un-checked-children="边框关"
                @change="tableBorderedchangeTheme"
              />
            </a-col>
          </a-row>
        </div>
      </a-form>
    </a-drawer>
  </div>
</template>
<script setup>
import { ref, reactive, h, provide, getCurrentInstance } from 'vue'
import { menuStore } from '@/stores/menu'
import { configStore } from '@/stores/config'
import { useUserStore } from '@/stores/user'
import router from '@/router'
import zhCN from 'ant-design-vue/es/locale/zh_CN'
import enUS from 'ant-design-vue/es/locale/en_US'
const user = useUserStore()
const username = user.userInfo.name
// 菜单状态
const menu = menuStore()
const config = configStore()
const collapsed = ref(false)
const openmenuKey = ref(menu.openmenuKey)
const selectmenuKey = ref(menu.selectmenuKey)
const panes = ref(menu.tagmenus)
// 定义一个布尔状态来跟踪是否处于全屏
const isFullScreen = ref(false)
const formConfig = reactive({
  themeClass: config.themeClass,
  tableBordered: config.tableBordered,
})
const menuItems = ref([])
const { proxy } = getCurrentInstance() // 获取当前实例
// 菜单项生成
const menuinit = () => {
  const icons = proxy.$icons // 访问全局图标
  const createMenuItem = menu => {
    return menu.map(item => {
      // 动态解析图标
      const iconComponent = item.icon ? icons[item.icon] : null
      const menuItem = {
        key: item.id,
        parentId: item.parentId,
        icon: iconComponent ? () => h(iconComponent) : undefined, // 动态渲染图标
        label: item.title,
        title: item.title,
        path: item.path,
      }
      if (item.children && item.children.length > 0) {
        menuItem.children = createMenuItem(item.children)
      }
      return menuItem
    })
  }
  menuItems.value = createMenuItem(menu.menus)
}
menuinit()
// 菜单点击事件
const menuclick = ({ item, key }) => {
  const existingTab = panes.value.find(x => x.key === key)
  if (!existingTab) {
    panes.value.push({
      key,
      parentId: item.parentId,
      title: item.title,
      path: item.path,
      closable: true,
    })
  }
  updateMenuPanes(key, item.parentId, item.path)
}
// 点击 Tag 切换选项卡
const tagchange = pane => {
  updateMenuPanes(pane.key, pane.parentId, pane.path)
}
// 删除 Tag
const tagdelete = pane => {
  if (pane.key == selectmenuKey.value[0]) {
    const index = panes.value.findIndex(item => item.key === pane.key)
    //如果有下一条
    if (index + 1 < panes.value.length) {
      updateMenuPanes(
        panes.value[index + 1].key,
        panes.value[index + 1].parentId,
        panes.value[index + 1].path,
      )
    } else if (index - 1 >= 0) {
      updateMenuPanes(
        panes.value[index - 1].key,
        panes.value[index - 1].parentId,
        panes.value[index - 1].path,
      )
    }
  }
  panes.value = panes.value.filter(x => x.key !== pane.key)
  menu.tagmenus = panes.value
}
// 下拉菜单点击事件
const dropdownonClick = ({ key }) => {
  const index = panes.value.findIndex(
    item => item.key === selectmenuKey.value[0],
  )
  switch (key) {
    case '1':
      panes.value = panes.value.filter(
        (item, i) => i >= index || item.key === 0,
      )
      menu.tagmenus = panes.value
      break
    case '2':
      panes.value = panes.value.filter((item, i) => i <= index)
      menu.tagmenus = panes.value
      break
    case '3':
      panes.value = panes.value.filter(
        x => x.key === selectmenuKey.value[0] || x.key === 0,
      )
      menu.tagmenus = panes.value
      break
    case '4':
      panes.value = panes.value.filter(x => x.key === 0)
      menu.tagmenus = panes.value
      updateMenuPanes('', '', '/console')
      break
    default:
      break
  }
}
const findAllByHref = (items, targetHref) => {
  let result = []
  items.forEach(item => {
    if (item.href === targetHref) {
      result.push(item)
    }
    if (item.children && item.children.length > 0) {
      result = result.concat(findAllByHref(item.children, targetHref))
    }
  })
  return result
}
//个人中心和退出
const avatardropdownonClick = ({ key }) => {
  switch (key) {
    case '1':
      const result = findAllByHref(menuItems.value, '/system/center')
      const existingTab = panes.value.find(x => x.key === result[0].key)
      if (!existingTab) {
        panes.value.push({
          key: result[0].key,
          parentId: result[0].parentId,
          title: result[0].title,
          path: result[0].path,
          closable: true,
        })
      }
      updateMenuPanes(result[0].key, result[0].parentId, '/system/center')
      break
    case '2':
      user.$reset()
      menu.$reset()
      router.push('/login')
      break
    default:
      break
  }
}
// 更新选中的选项卡和路由
const updateMenuPanes = (selKey, openKey, path) => {
  selectmenuKey.value = [selKey]
  openmenuKey.value = [openKey]
  menu.setSelectmenuKey(selectmenuKey.value)
  menu.setOpenmenuKey(openmenuKey.value)
  router.push(path)
}
//国际化
const dropdownonLanguageClick = ({ key }) => {
  if (key === 'zhCN') {
    config.setLocale(zhCN)
  } else {
    config.setLocale(enUS)
  }
}
// 抽屉状态
const open = ref(false)
const showDrawer = () => {
  open.value = true
}
const onClose = () => {
  open.value = false
}
// 主题切换
const themeClasschangeTheme = e => {
  config.themeClass = e.target.value
}
// 定义全屏切换方法
const toggleFullScreen = () => {
  const element = document.documentElement
  if (!isFullScreen.value) {
    // 进入全屏模式
    if (element.requestFullscreen) {
      element.requestFullscreen()
    } else if (element.mozRequestFullScreen) {
      // Firefox
      element.mozRequestFullScreen()
    } else if (element.webkitRequestFullscreen) {
      // Chrome, Safari and Opera
      element.webkitRequestFullscreen()
    } else if (element.msRequestFullscreen) {
      // IE/Edge
      element.msRequestFullscreen()
    }
  } else {
    // 退出全屏模式
    if (document.exitFullscreen) {
      document.exitFullscreen()
    } else if (document.mozCancelFullScreen) {
      // Firefox
      document.mozCancelFullScreen()
    } else if (document.webkitExitFullscreen) {
      // Chrome, Safari and Opera
      document.webkitExitFullscreen()
    } else if (document.msExitFullscreen) {
      // IE/Edge
      document.msExitFullscreen()
    }
  }
  isFullScreen.value = !isFullScreen.value // 切换全屏状态
}
//表格边框切换
const tableBorderedchangeTheme = checked => {
  config.tableBordered = checked
}
const reload = () => {
  window.location.reload()
}
const scrollContainer = ref(null)
const onWheelScroll = e => {
  if (scrollContainer.value) {
    scrollContainer.value.scrollLeft += e.deltaY // 将垂直滚动转为水平滚动
  }
}
// 提供方法
provide('menuinit', menuinit)
provide('menuclick', menuclick)
</script>
<style scoped>
.trigger {
  font-size: 18px;
  line-height: 64px;
  padding: 0 24px;
  cursor: pointer;
  transition: color 0.3s;
}
.trigger:hover {
  color: #1890ff;
}
.logo {
  text-align: center;
  line-height: 32px;
  height: 32px;
  margin: 16px;
  font-size: 16px;
}
.site-layout .site-layout-background {
  background: #fff;
}
.zc-layout-header {
  background: #fff !important;
  padding: 0 !important;
  border-bottom: 1px solid rgba(5, 5, 5, 0.06);
}
.zc-layout-content-tabs {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 69px;
}
.tag {
  background-color: #fff;
  color: #999999;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
  font-size: 14px;
  padding: 0 15px;
}
.tag-select {
  background-color: #fff;
  color: #1677ff;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
  font-size: 14px;
  padding: 0 15px;
}
.zc-layout-content-tabs-left {
  flex: 0.95;
  padding-left: 15px;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  /* 确保只有横向滑动 */
  width: 100%;
  /* 父容器宽度 */
  -ms-overflow-style: none;
  /* 适用于 IE 和 Edge */
  scrollbar-width: none;
  /* 适用于 Firefox */
}
.zc-layout-content-tabs-left::-webkit-scrollbar {
  display: none;
  /* 隐藏 Webkit 浏览器中的滚动条 */
}
.zc-layout-content-tabs-right {
  flex: 0.05;
  text-align: right;
  padding-right: 15px;
}
.zc-layout-content-tabs-right-dropdown {
  background-color: white;
  padding: 8px 10px;
  border-radius: 4px;
}
</style>
