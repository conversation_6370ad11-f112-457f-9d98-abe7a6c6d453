<template>
  <div class="bg-color-white flex-row">
    <div class="flex-item-1">
      <a-tabs
        class="padding-left-20"
        v-model:activeKey="activeKey"
        @change="activeKeyChange"
      >
        <a-tab-pane key="1">
          <template #tab>
            <a-badge>冷热源</a-badge>
          </template>
        </a-tab-pane>
        <a-tab-pane key="2" tab="热源" force-render></a-tab-pane>
      </a-tabs>
    </div>
    <div
      class="flex-item-9 text-color-red"
      style="padding-bottom: 18px; padding-left: 20px"
    ></div>
  </div>
  <form-table
    :columns="filteredColumns"
    modulePath="ColdHeatSources"
    method="get"
    pageAction="getList"
    :where="localRecordwhere"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    :page="false"
    @edit="edit"
    ref="childRef"
  >
    <template #custom-coldHeatSourcesMode="{ record }">
      {{ record.coldHeatSourcesMode == 0 ? '不供冷' : '供冷' }}
    </template>
  </form-table>
  <!-- 编辑 -->
  <ColdHeatSourcesEdit
    :editTitle="editTitle"
    :open="editopen"
    :formInfo="formInfo"
    @close="editopen = false"
    @updateData="refreshData"
  />
</template>
<script setup>
import { ref, defineProps, reactive, nextTick, computed, inject } from 'vue'
import FormTable from '../../FormTable.vue'
import ColdHeatSourcesEdit from '../../edit/ColdHeatSourcesEdit.vue'
import coldHeatSources from '@/api/methods/coldHeatSources'
// 注入父组件的方法
const getproject = inject('getproject')
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const activeKey = ref('1')
// 创建一个响应式的本地副本
const localRecordwhere = ref({ ...props.recordwhere })

const columns = [
  {
    title: '冷热源名称',
    dataIndex: 'coldHeatSourcesName',
    resizable: true,
  },
  {
    title: 'DO开关端口',
    dataIndex: 'coldHeatSourcesDO',
  },
  {
    title: 'DO模式端口',
    dataIndex: 'coldHeatSourcesModeDO',
    key: 'coldHeatSourcesModeDO',
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
  },
]
const childRef = ref(null)
//编辑
const defaultformInfo = {
  id: '',
  projectId: localRecordwhere.value.projectId,
  coldHeatSourcesType: 1,
  coldHeatSourcesName: '',
  coldHeatSourcesDO: 0,
  coldHeatSourcesModeDO: 0,
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editopen = ref(false)
const editTitle = ref('新增')

const edit = record => {
  if (record.id) {
    editTitle.value = '修改'
    coldHeatSources.get({ id: record.id }).then(res => {
      Object.assign(formInfo, res.data)
      editopen.value = true
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
    var number = childRef.value.getDataSource().length + 1
    formInfo.coldHeatSourcesName =
      localRecordwhere.value.coldHeatSourcesType == '1'
        ? number + '号冷源'
        : number + '号热源'

    formInfo.coldHeatSourcesType = activeKey.value
    editopen.value = true
  }
}

// 计算属性，根据 activeKey 动态决定列的显示
const filteredColumns = computed(() => {
  // 当 activeKey 为 '2' 时，隐藏 'coldHeatSourcesMode' 列
  if (activeKey.value === '2') {
    return columns.filter(col => col.key !== 'coldHeatSourcesModeDO')
  }
  return columns
})

const activeKeyChange = key => {
  localRecordwhere.value = {
    projectId: props.recordwhere.projectId,
    coldHeatSourcesType: key,
  }
  activeKey.value = key
  nextTick(() => {
    refreshData()
  })
}
const refreshData = () => {
  childRef.value.tableLoad()
  getproject()
}
defineExpose({
  refreshData,
})
</script>
<style scoped></style>
