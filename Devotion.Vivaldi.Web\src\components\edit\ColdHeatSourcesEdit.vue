<template>
  <!-- 详情 -->
  <a-drawer
    :title="editTitle"
    width="40%"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-form-item
        label="冷热源名称"
        name="coldHeatSourcesName"
        :rules="[{ required: true, message: '冷热源名称不能为空' }]"
      >
        <a-input
          v-model:value="formInfo.coldHeatSourcesName"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        label="DO开关端口"
        name="coldHeatSourcesDO"
        :rules="[{ required: true, message: 'DO开关端口不能为空' }]"
      >
        <a-input-number
          :min="0"
          :max="80"
          :precision="0"
          v-model:value="formInfo.coldHeatSourcesDO"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item
        label="DO模式端口"
        name="coldHeatSourcesMode"
        v-if="formInfo.coldHeatSourcesType == 1"
      >
        <a-input-number
          :min="0"
          :max="80"
          :precision="0"
          v-model:value="formInfo.coldHeatSourcesModeDO"
          placeholder="请输入"
        />
      </a-form-item>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup>
import { defineProps, defineEmits, ref } from 'vue'
import { message } from 'ant-design-vue'
import coldHeatSources from '@/api/methods/coldHeatSources'
const props = defineProps({
  editTitle: {
    type: String,
    default: '新增',
  },
  open: {
    type: Boolean,
    required: true,
  },
  formInfo: {
    type: Object,
    required: true,
  },
})
const formRef = ref(null)

const onSave = () => {
  //提交数据
  formRef.value.validate().then(() => {
    if (props.formInfo.id == '') {
      coldHeatSources
        .add(props.formInfo)
        .then(() => {
          message.success('成功', 1, () => {
            onClose()
            emit('updateData') // 触发事件
          })
        })
        .catch(error => {})
    } else {
      coldHeatSources
        .update(props.formInfo)
        .then(() => {
          message.success('成功', 1, () => {
            onClose()
            emit('updateData') // 触发事件
          })
        })
        .catch(error => {})
    }
  })
}
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close', 'updateData'])
const onClose = () => {
  emit('close')
}
</script>
