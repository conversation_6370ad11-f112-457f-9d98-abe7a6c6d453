<template>
  <a-modal
    :open="open"
    title="详情"
    width="60%"
    wrap-class-name="full-modal"
    @cancel="onClose"
    :destroyOnClose="true"
  >
    <a-descriptions title="" bordered :column="3">
      <a-descriptions-item label="项目名称">
        {{ info.projectName }}
      </a-descriptions-item>
      <a-descriptions-item label="项目进度">
        <span
          v-html="
            dictTemplate.tabletempInt('projectProgress', info.projectProgress)
          "
        ></span>
      </a-descriptions-item>
      <a-descriptions-item label="是否归属小区">
        {{ info.isCommunity ? '是' : '否' }}
      </a-descriptions-item>
      <a-descriptions-item label="小区" span="3">
        {{ info.communityName }}
      </a-descriptions-item>
      <a-descriptions-item label="省/市/区">
        {{ info.province }}/{{ info.city }}/{{ info.district }}
      </a-descriptions-item>
      <a-descriptions-item label="详细地址" span="2">
        {{ info.detailedAddress }}
      </a-descriptions-item>
      <a-descriptions-item label="业主姓名">
        {{ info.ownerName }}
      </a-descriptions-item>
      <a-descriptions-item label="业主电话">
        {{ info.ownerPhone }}
      </a-descriptions-item>
      <!-- <a-descriptions-item label="业主邮箱">
        {{ info.ownerEmail }}
      </a-descriptions-item> -->
      <a-descriptions-item label="总面积（单位：平米）">
        {{ info.totalArea }}
      </a-descriptions-item>
      <a-descriptions-item label="楼层数">
        {{ info.floor }}
      </a-descriptions-item>
      <a-descriptions-item label="房间数">
        {{ info.numberRooms }}
      </a-descriptions-item>
      <a-descriptions-item label="经销商">
        {{ info.dealerName }}
      </a-descriptions-item>
      <a-descriptions-item label="开工时间">
        {{ info.startTime != null ? info.startTime.split(' ')[0] : '' }}
      </a-descriptions-item>
      <a-descriptions-item label="竣工时间">
        {{
          info.completionTime != null ? info.completionTime.split(' ')[0] : ''
        }}
      </a-descriptions-item>
      <a-descriptions-item label="工程经理">
        {{ info.engineeringManager }}
      </a-descriptions-item>
      <a-descriptions-item label="工程设计">
        {{ info.engineeringDesign }}
      </a-descriptions-item>
      <!-- <a-descriptions-item label="工程调试">
        {{ info.engineeringDebug }}
      </a-descriptions-item> -->
      <a-descriptions-item label="工程售后">
        {{ info.engineeringAftersales }}
      </a-descriptions-item>
      <a-descriptions-item label="竣工图纸" span="2">
        <div
          v-if="
            info.engineeringDrawings != null && info.engineeringDrawings != ''
          "
          v-for="(item, index) in JSON.parse(info.engineeringDrawings)"
        >
          <a :href="item.url">{{ item.name }}</a>
        </div>
      </a-descriptions-item>

      <a-descriptions-item label="创建人">
        {{ info.accountName }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">
        {{ info.createTime }}
      </a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button key="submit" type="primary" @click="onClose">关闭</a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { defineProps, defineEmits } from 'vue'
import dictTemplate from '@/utils/dictTemplate'
const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
  info: {
    type: Object,
    required: true,
  },
})
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close'])
const onClose = () => {
  emit('close')
}
</script>
