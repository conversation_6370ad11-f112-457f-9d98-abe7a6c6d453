import { post, get } from '@/api/request'

const radiationZone = {
  //根据id获取信息
  get(params) {
    return get('RadiationZone/get', params)
  },
  ///根据projectId和zoneType获取辐射区信息
  getByProjectIdorZoneTypeList(params) {
    return get('RadiationZone/getByProjectIdorZoneTypeList', params)
  },

  /// 根据辐射区id获取运行参数
  getRZCProjectOverallSetting(params) {
    return get('RadiationZone/getRZCProjectOverallSetting', params)
  },
  getRZCScThermoelectricValve(params) {
    return get('RadiationZone/getRZCScThermoelectricValve', params)
  },
  GetRZCInfo1(params) {
    return get('RadiationZone/GetRZCInfo1', params)
  },

  //获取传感器配置信息(未选择房间信息)
  getNoSensorConfigList(params) {
    return get('RadiationZone/getNoSensorConfigList', params)
  },
  //新增
  add(params) {
    return post('RadiationZone/add', params, true)
  },
  //更新
  update(params) {
    return post('RadiationZone/update', params, true)
  },
  //获取辐射区分区
  getZoneNoList(params) {
    return get('RadiationZone/getZoneNoList', params)
  },

  //辐射区基础设定保存
  basicSettings(params) {
    return post('RadiationZone/BasicSettings', params, true)
  },

  //辐射区供冷供暖逻辑设定
  glGnSettings(params) {
    return post('RadiationZone/GlGnSettings', params, true)
  },
  //房间设置
  scSettings(params) {
    return post('RadiationZone/ScSettings', params, true)
  },
}
//
export default radiationZone
