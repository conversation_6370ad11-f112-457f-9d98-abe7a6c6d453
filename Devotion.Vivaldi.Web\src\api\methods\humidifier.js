import { post, get } from '@/api/request'

const humidifier = {
  //根据id获取信息
  get(params) {
    return get('humidifier/get', params, true)
  },
  //新增
  add(params) {
    return post('humidifier/add', params, true)
  },
  //更新
  update(params) {
    return post('humidifier/update', params, true)
  },
  // 根据projectId获取加湿器信息
  getList(params) {
    return get('humidifier/getList', params)
  },
  //  根据新风机查询所属房间
  getSensorConfigByfafid(params) {
    return get('humidifier/GetSensorConfigByfafid', params)
  },
}
//
export default humidifier
