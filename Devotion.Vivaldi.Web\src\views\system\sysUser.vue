<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="sysUser"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    @edit="edit"
    ref="childRef"
  >
    <!-- 可以通过插槽自定义单元格 -->
    <template #custom-isEnable="{ record }">
      <a-switch
        v-model:checked="record.isEnable"
        checked-children="是"
        un-checked-children="否"
        @change="handleSwitchChange(record)"
      />
    </template>
  </form-table>
  <!-- 新增修改 -->
  <div>
    <a-drawer
      :title="editTitle"
      :width="540"
      :open="open"
      :body-style="{ paddingBottom: '80px' }"
      :footer-style="{ textAlign: 'right' }"
      @close="onClose"
    >
      <a-form :model="formInfo" ref="formRef" layout="vertical" :rules="rules">
        <a-form-item label="账号名称" name="name">
          <a-input v-model:value="formInfo.name" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="手机号" name="mobile">
          <a-input v-model:value="formInfo.mobile" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="密码" name="password" v-if="formInfo.id == 0">
          <a-input-password
            v-model:value="formInfo.password"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item label="角色" name="roleId">
          <a-select
            v-model:value="formInfo.roleId"
            placeholder="请选择"
            :options="roleList"
          >
          </a-select>
        </a-form-item>
        <a-form-item label="性别" name="sex">
          <a-radio-group v-model:value="formInfo.sex">
            <a-radio :value="1">男</a-radio>
            <a-radio :value="2">女</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="年龄" name="age">
          <a-input-number v-model:value="formInfo.age" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="邮件" name="email">
          <a-input v-model:value="formInfo.email" placeholder="请输入" />
        </a-form-item>

        <a-form-item label="是否启用" name="isEnable">
          <a-switch v-model:checked="formInfo.isEnable" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formInfo.remark" />
        </a-form-item>
      </a-form>
      <template #extra>
        <a-space>
          <a-button type="primary" @click="onSave">保存</a-button>
          <a-button @click="() => formRef.resetFields()">重置</a-button>
        </a-space>
      </template>
    </a-drawer>
  </div>
</template>
<script setup>
import { ref, reactive, toRaw, onMounted } from 'vue'
import FormTable from '../../components/FormTable.vue'
import sysUser from '@/api/methods/sysUser'
import sysRole from '@/api/methods/sysRole'
import { message } from 'ant-design-vue'
const formState = ref({
  name: { label: '账号名称', value: '', type: 'text' },
  mobile: { label: '手机号', value: '', type: 'text' },
  roleId: {
    label: '角色',
    value: null,
    type: 'select',
    data: [],
  },
  createTime: { label: '日期范围', value: '', type: 'time' },
})

const fetchRoleData = async () => {
  sysRole.getList().then(res => {
    formState.value.roleId.data = res.data.map(role => ({
      label: role.name,
      value: role.id,
    }))
  })
}
// 使用 onMounted 在组件加载时调用 fetchRoleData
onMounted(() => {
  fetchRoleData()
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
  },
  {
    title: '账号名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '手机号',
    dataIndex: 'mobile',
    key: 'mobile',
  },
  {
    title: '角色',
    dataIndex: 'roleName',
    key: 'roleName',
  },
  {
    title: '备注',
    key: 'remark',
    dataIndex: 'remark',
  },
  {
    title: '是否启用',
    key: 'isEnable',
    dataIndex: 'isEnable',
  },
  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const formInfo = reactive({
  id: 0,
  name: '',
  mobile: '',
  password: '',
  sex: 1,
  age: 0,
  email: '',
  roleId: null,
  isEnable: true,
  remark: '',
})
const rules = {
  name: [
    {
      required: true,
      message: '账号名称不能为空',
      trigger: 'change',
    },
  ],
  mobile: [
    {
      required: true,
      message: '手机号不能为空',
      trigger: 'change',
    },
  ],
  password: [
    {
      required: true,
      message: '密码不能为空',
      trigger: 'change',
    },
  ],
}
const open = ref(false)
const editTitle = ref('新增')
const roleList = ref([])
const childRef = ref(null)
// 引用表单的ref
const formRef = ref(null)
//新增
const edit = record => {
  sysRole.getList().then(res => {
    roleList.value = res.data.map(x => ({
      label: x.name,
      value: x.id,
    }))
    // 触发自定义事件，父组件会监听这个事件
    if (record.id) {
      editTitle.value = '修改'
      sysUser.get({ id: record.id }).then(res => {
        Object.assign(formInfo, res.data)
      })
    } else {
      editTitle.value = '新增'
      formInfo.id = 0
      formInfo.password = ''
    }
    open.value = true
  })
}

const onSave = () => {
  formRef.value
    .validate()
    .then(() => {
      sysUser.save(toRaw(formInfo)).then(() => {
        message.success('成功', 1)
        open.value = false
        // open.value = false;
        formRef.value.resetFields()
        childRef.value.tableLoad()
      })
    })
    .catch(error => {
      console.log('error', error)
    })
}
//修改是否启用
const handleSwitchChange = record => {
  var data = {
    id: record.id,
    isEnabled: record.isEnable,
  }
  sysUser.updateIsEnabled(data).then(() => {
    childRef.value.tableLoad()
  })
}
const onClose = () => {
  formRef.value.resetFields()
  open.value = false
}
</script>

<style></style>
