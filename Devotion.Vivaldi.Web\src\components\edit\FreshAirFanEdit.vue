<template>
  <!-- 详情 -->
  <a-drawer
    :title="editTitle"
    width="60%"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item
            label="分区编号"
            name="zoneNo"
            :rules="[{ required: true, message: '分区编号不能为空' }]"
          >
            <a-select
              v-model:value="formInfo.zoneNo"
              placeholder="请选择"
              :options="formInfo.zoneNoOptions"
              @change="zoneNoChange"
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label="新风机名称"
            name="freshAirFanName"
            :rules="[{ required: true, message: '新风机名称不能为空' }]"
          >
            <a-input
              v-model:value="formInfo.freshAirFanName"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label="新风机类型"
            name="freshAirFanType"
            :rules="[{ required: true, message: '请选择新风机类型' }]"
          >
            <a-select
              v-model:value="formInfo.freshAirFanType"
              placeholder="请选择"
            >
              <a-select-option :value="1">ivy500</a-select-option>
              <a-select-option :value="2">ivy350</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label="新风机地址"
            name="freshAirFanAdress"
            :rules="[{ required: true, message: '新风机地址不能为空' }]"
          >
            <a-input
              v-model:value="formInfo.freshAirFanAdress"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="6">
          <a-form-item
            label="Modbus接口"
            name="modbus"
            :rules="[{ required: true, message: 'Modbus接口不能为空' }]"
          >
            <a-input-number
              style="width: 100%"
              :min="0"
              :precision="0"
              v-model:value="formInfo.modbus"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label="新风机电源DO"
            name="freshAirFanPowerSupplyDO"
            :rules="[{ required: true, message: '新风机电源DO不能为空' }]"
          >
            <a-input-number
              style="width: 100%"
              :min="0"
              :max="80"
              :precision="0"
              v-model:value="formInfo.freshAirFanPowerSupplyDO"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item
            label="高温泵"
            name="highTemperaturePumpId"
            :rules="[{ required: true, message: '请选择高温泵' }]"
          >
            <a-select
              v-model:value="formInfo.highTemperaturePumpId"
              placeholder="请选择"
              :options="highTemperaturePumpList"
              @change="communityChange"
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="操作">
            <a-button type="primary" @click="addHighTemperaturePump"
              >新增高温泵</a-button
            >
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="24">
          <a-flex gap="middle" horizontal align="center">
            <div>风阀数量</div>
            <div>（现有风阀 {{ formInfo.airValveNumber }} 个！）</div>
            <div>
              <a-input-number
                :min="0"
                :precision="0"
                v-model:value="airValveNumbernew"
                placeholder="请输入"
              />
            </div>
            <div>
              <a-button type="primary" @click="addAirValve">新增风阀</a-button>
            </div>
          </a-flex>
        </a-col>
      </a-row>
      <div class="margin-top-20">
        <a-table
          :dataSource="formInfo.fAFAirValve"
          :columns="columns"
          :pagination="false"
          bordered
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.key === 'airValveDO'">
              <a-input-number
                style="width: 100%"
                :min="0"
                :precision="0"
                v-model:value="record.airValveDO"
                placeholder="请输入"
              />
            </template>
            <template v-if="column.key === 'sensorConfig'">
              <a-radio-group
                name="fafradiogroup"
                :options="record.sensorConfigOptions"
                v-model:value="record.sensorConfigId"
              />
            </template>
            <template v-if="column.key === 'operation'">
              <a-button type="primary" danger @click="handleDelete(index)"
                >删除</a-button
              >
            </template>
          </template>
        </a-table>
      </div>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>

  <a-modal
    v-model:open="highTemperaturePumpopen"
    title="新增高温泵"
    width="70%"
    wrap-class-name="full-modal"
    :destroyOnClose="true"
    :afterClose="getList"
  >
    <HighTemperaturePumpPage :projectId="formInfo.projectId" ref="childPageRef">
    </HighTemperaturePumpPage>
    <template #footer>
      <a-button
        key="submit"
        type="primary"
        @click="highTemperaturePumpopen = false"
        >关闭</a-button
      >
    </template>
  </a-modal>
</template>
<script setup>
import { defineProps, defineEmits, ref, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import freshAirFan from '@/api/methods/freshAirFan'
import highTemperaturePump from '@/api/methods/highTemperaturePump'
import HighTemperaturePumpPage from '../pages/projectConfig/HighTemperaturePumpPage.vue'
const props = defineProps({
  editTitle: {
    type: String,
    default: '新增',
  },
  open: {
    type: Boolean,
    required: true,
  },
  formInfo: {
    type: Object,
    required: true,
  },
})

const airValveNumbernew = ref(0)
const formRef = ref(null)
//
const columns = [
  {
    title: '风阀DO',
    dataIndex: 'airValveDO',
    key: 'airValveDO',
    width: 150,
  },
  {
    title: '隶属房间',
    dataIndex: 'sensorConfig',
    key: 'sensorConfig',
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
]
const highTemperaturePumpList = ref([])
const getList = () => {
  highTemperaturePump
    .getList({ projectId: props.formInfo.projectId })
    .then(res => {
      var hList = [
        {
          label: '暂无',
          value: '00000000-0000-0000-0000-000000000000',
        },
      ]
      res.data.forEach(x => {
        hList.push({
          label:
            x.highTemperaturePumpName + '_端口号_' + x.highTemperaturePumpDO,
          value: x.id,
        })
      })
      highTemperaturePumpList.value = hList
    })
}
getList()
const highTemperaturePumpopen = ref(false)
const childPageRef = ref(null)
const addHighTemperaturePump = () => {
  highTemperaturePumpopen.value = true
  nextTick(() => {
    if (childPageRef.value) {
      childPageRef.value.refreshData() // 调用子组件暴露的方法
    }
  })
}

//新增风阀
const addAirValve = () => {
  const filteredOptions = props.formInfo.roomOptions

  var fAFAirValvelist = props.formInfo.fAFAirValve
  for (let i = 0; i < airValveNumbernew.value; i++) {
    fAFAirValvelist.push({
      id: '',
      freshAirFanId: '',
      airValveDO: 0,
      sensorConfigId: '',
      sensorConfigOptions: filteredOptions,
    })
  }
  props.formInfo.fAFAirValve = fAFAirValvelist
  airValveNumbernew.value = 0
}
// 自定义删除功能
const handleDelete = index => {
  // 找到要删除的记录的索引
  if (index !== -1) {
    // 从数据源中移除该记录
    props.formInfo.fAFAirValve.splice(index, 1)
  }
}
const onSave = () => {
  //console.log(props.formInfo)
  //提交数据
  formRef.value.validate().then(() => {
    if (
      props.formInfo.highTemperaturePumpId ==
      '00000000-0000-0000-0000-000000000000'
    ) {
      message.error('请选择添加高温泵', 1)
      return
    }
    if (props.formInfo.fAFAirValve.length == 0) {
      message.error('请添加风阀', 1)
      return
    }
    var isok = true
    props.formInfo.fAFAirValve.forEach(x => {
      if (x.sensorConfigId == '') {
        message.error('风阀DO[' + x.airValveDO + ']的隶属房间不能为空', 1)
        isok = false
        return false
      }
    })
    if (!isok) {
      return
    }
    if (props.formInfo.id == 0) {
      freshAirFan
        .add(props.formInfo)
        .then(() => {
          message.success('成功', 1, () => {
            onClose()
            emit('updateData') // 触发事件
          })
        })
        .catch(error => {})
    } else {
      freshAirFan
        .update(props.formInfo)
        .then(() => {
          message.success('成功', 1, () => {
            onClose()
            emit('updateData') // 触发事件
          })
        })
        .catch(error => {})
    }
  })
}
//选中分区
const zoneNoChange = value => {
  props.formInfo.freshAirFanName = value + '号新风机'
}
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close', 'updateData'])
const onClose = () => {
  emit('close')
}
</script>
