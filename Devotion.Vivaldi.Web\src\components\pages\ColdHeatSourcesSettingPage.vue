<template>
  <!--冷热源设置-->
  <a-breadcrumb style="background-color: #fff; padding: 0 0 10px 10px">
    <a-breadcrumb-item
      ><a @click="changeActive(1)">系统设置</a></a-breadcrumb-item
    >
    <a-breadcrumb-item>冷热源</a-breadcrumb-item>
  </a-breadcrumb>
  <div>
    <a-card>
      <div v-if="coldHeatSourceslist.length > 0">
        <a-row :gutter="16">
          <a-col :span="4">
            <div
              v-for="(item, index) in coldHeatSourceslist"
              :key="item.id"
              class="listitem"
              :class="{ listitemselected: selectedIndex === index }"
              @click="handleItemClick(item.id, index, item.coldHeatSourcesName)"
            >
              {{ item.coldHeatSourcesName }}
            </div>
          </a-col>
          <a-col :span="20">
            <a-descriptions
              :title="coldHeatSourcesinfo.coldHeatSourcesName + '的运行信息'"
              layout="vertical"
              bordered
              :column="10"
            >
              <a-descriptions-item label="运行模式">
                {{ yxmodel }}
              </a-descriptions-item>
              <a-descriptions-item label="系统状态">
                {{ systatus }}
              </a-descriptions-item>
              <a-descriptions-item label="工作模式">
                <span v-if="projectOverallSettings.operatingMode == 0"
                  >供冷</span
                >
                <span v-if="projectOverallSettings.operatingMode == 1"
                  >供暖</span
                >
                <span v-if="projectOverallSettings.operatingMode == 2"
                  >通风</span
                >
              </a-descriptions-item>
              <a-descriptions-item label="室外温度	">
                {{
                  projectOverallSettings.outdoorTemperature
                }}℃</a-descriptions-item
              >
              <a-descriptions-item label="室外湿度	">
                {{
                  projectOverallSettings.outdoorHumidity
                }}%</a-descriptions-item
              >
              <a-descriptions-item label="开关状态">
                {{ coldHeatSourcesinfo.getColdHeatSourcesStatus ? '开启' : '关闭' }}
              </a-descriptions-item>
              <a-descriptions-item
                v-if="coldHeatSourcesinfo.coldHeatSourcesType == 1"
                label="模式状态"
              >
                {{
                  coldHeatSourcesinfo.getColdHeatSourcesModeStatus
                    ? '开启'
                    : '关闭'
                }}
              </a-descriptions-item>
            </a-descriptions>
            <div class="bg-color-white padding-top-10">
              <a-card
                :title="coldHeatSourcesinfo.coldHeatSourcesName + '的设置'"
              >
                <div class="text-font-18 padding-bottom-10">基础设定</div>
                <a-row :gutter="16">
                  <a-col :span="12">
                    <span class="text-font-16 padding-20"
                      >开关状态(DO端口：{{
                        coldHeatSourcesinfo.coldHeatSourcesDO
                      }})</span
                    >
                    <a-radio-group
                      v-model:value="
                        coldHeatSourcesinfo.setColdHeatSourcesStatus
                      "
                      name="switchModeGroup"
                    >
                      <a-radio :value="0">关闭</a-radio>
                      <a-radio :value="1">开启</a-radio>
                      <a-radio :value="2">自动</a-radio>
                    </a-radio-group>
                  </a-col>
                  <a-col
                    :span="12"
                    v-if="coldHeatSourcesinfo.coldHeatSourcesType == 1"
                  >
                    <span class="text-font-16 padding-20"
                      >冷源模式(DO端口：{{
                        coldHeatSourcesinfo.coldHeatSourcesModeDO
                      }})</span
                    >
                    <a-radio-group
                      v-model:value="
                        coldHeatSourcesinfo.setColdHeatSourcesModeStatus
                      "
                      name="heatSourcesModeGroup"
                    >
                      <a-radio :value="0">关闭</a-radio>
                      <a-radio :value="1">开启</a-radio>
                      <a-radio :value="2">自动</a-radio>
                    </a-radio-group>
                  </a-col>
                </a-row>
                <div style="text-align: right; margin: 20px 0 0 0">
                  <a-space>
                    <a-button type="primary" @click="basicSettings"
                      >保存</a-button
                    >
                  </a-space>
                </div>
              </a-card>
            </div>
          </a-col>
        </a-row>
      </div>
      <div v-else><a-empty /></div>
    </a-card>
  </div>
</template>
<script setup>
import {
  defineProps,
  defineEmits,
  ref,
  onMounted,
  onBeforeUnmount,
  inject,
} from 'vue'
import coldHeatSources from '@/api/methods/coldHeatSources'
import { message } from 'ant-design-vue'
const userInfo = inject('userInfo')
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const projectId = props.recordwhere.projectId
const coldHeatSourceslist = ref([])
const coldHeatSourcesinfo = ref({})
const projectOverallSettings = ref({})
const settiingshow = ref(false)
const init = () => {
  coldHeatSources.getByProjectIdList({ projectId }).then(res => {
    coldHeatSourceslist.value = res.data
    if (coldHeatSourceslist.value.length > 0) {
      getInfo(res.data[0].id)
    }
  })
}
init()
const selectedIndex = ref(0) // 默认选中第一个项
const handleItemClick = (id, index, coldHeatSourcesName) => {
  selectedIndex.value = index
  getInfo(id)
}
//运行模式
const yxmodel = ref('')
//系统状态
const systatus = ref('')
const getInfo = id => {
  coldHeatSources
    .getColdHeatSourcesSetting({ coldHeatSourcesId: id })
    .then(res => {
      coldHeatSourcesinfo.value = res.data.coldHeatSources
      projectOverallSettings.value = res.data.projectOverallSettings

      yxmodel.value =
        res.data.projectOverallSettings.seasonMode == 0 ? '手动' : '自动'
      systatus.value =
        res.data.projectOverallSettings.workingCondition == 0
          ? '关闭'
          : res.data.projectOverallSettings.workingCondition == 1
            ? '运行'
            : res.data.projectOverallSettings.workingCondition == 2
              ? res.data.projectOverallSettings.automaticWorking == 1
                ? '值班'
                : res.data.projectOverallSettings.automaticWorking == 2
                  ? '值班'
                  : '未知'
              : '未知'
    })
}
//基础设定保存
const basicSettings = () => {
  if (!userInfo.value.isConfig) {
    message.error('暂无配置权限，无法进行操作')
    return
  }
  coldHeatSources.basicSettings(coldHeatSourcesinfo.value).then(res => {
    message.success('成功', 1, () => {
      init()
    })
  })
}
// 定义定时器变量
let timer = null
// 启动定时器
const startTimer = () => {
  timer = setInterval(() => {
    getInfo(coldHeatSourceslist.value[selectedIndex.value].id)
  }, 5000)
}

// 在组件挂载后启动定时器
onMounted(() => {
  // startTimer()
})

// 在组件销毁前清除定时器
onBeforeUnmount(() => {
  if (timer) {
    // clearInterval(timer)
  }
})
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close'], ['change-active'])
const onClose = () => {
  emit('close')
}

const changeActive = value => {
  emit('change-active', value)
}
</script>
<style scoped>
.listitem {
  padding: 15px;
  border: 1px solid #f0f0f0;
  cursor: pointer;
}
.listitemselected {
  background-color: #1677ff;
  color: white;
}
</style>
