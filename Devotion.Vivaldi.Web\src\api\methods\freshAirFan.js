import { post, get } from '@/api/request'

const freshAirFan = {
  //根据id获取信息
  get(params) {
    return get('freshAirFan/get', params)
  },
  //根据projectId查询新风机信息
  getList(params) {
    return get('freshAirFan/getList', params)
  },
  //根据projectId获取新风机信息Options
  getOptionsList(params) {
    return get('freshAirFan/getOptionsList', params)
  },
  //新增
  add(params) {
    return post('freshAirFan/add', params, true)
  },
  //更新
  update(params) {
    return post('freshAirFan/update', params, true)
  },
  //获取传感器配置信息(未选择房间信息)
  getNoSensorConfigList(params) {
    return get('freshAirFan/getNoSensorConfigList', params)
  },
  //根据id查询新风机里面的房间信息Options
  getfafSensorConfigOptions(params) {
    return get('freshAirFan/getfafSensorConfigOptions', params)
  },
  // 根据projectId获取新风机信息
  getByProjectIdList(params) {
    return get('FreshAirFan/getByProjectIdList', params)
  },
  // 根据新风机id获取运行信息
  getFaFProjectOverallSetting(params) {
    return get('FreshAirFan/getFaFProjectOverallSetting', params,true)
  },
  // 根据新风机id获取运行参数
  getFreshAirFanCoolingParameters(params) {
    return get('FreshAirFan/getFreshAirFanCoolingParameters', params)
  },
  // 根据新风机id获取运行模式信息Select
  getSelectOperatingMode(params) {
    return get('FreshAirFan/getSelectOperatingMode', params)
  },
  //获取新风区分区
  getZoneNoList(params) {
    return get('freshAirFan/getZoneNoList', params)
  },

  //新风区基础设定保存
  basicSettings(params) {
    return post('freshAirFan/BasicSettings', params, true)
  },

  //新风区供冷供暖模式设定
  glGnSettings(params) {
    return post('freshAirFan/GlGnSettings', params, true)
  },
  //房间设置
  scSettings(params) {
    return post('freshAirFan/ScSettings', params, true)
  },
  getSensorConfigOptionsList(params) {
    return get('freshAirFan/getSensorConfigOptionsList', params)
  },
}
//
export default freshAirFan
