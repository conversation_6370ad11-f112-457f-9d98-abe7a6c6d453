import { post, get } from '@/api/request'

const coldHeatSources = {
  //根据id获取信息
  get(params) {
    return get('ColdHeatSources/get', params)
  },
  //根据id获取信息
  getByProjectIdList(params) {
    return get('ColdHeatSources/getByProjectIdList', params)
  },
  //根据projectId和coldHeatSourcesType 获取冷热源信息
  getList(params) {
    return get('ColdHeatSources/getList', params)
  },
  //新增
  add(params) {
    return post('ColdHeatSources/add', params, true)
  },
  //更新
  update(params) {
    return post('ColdHeatSources/update', params, true)
  },
  //根据冷热源id获取配置信息
  getColdHeatSourcesSetting(params) {
    return get('ColdHeatSources/getColdHeatSourcesSetting', params, true)
  },

  //新风区基础设定保存
  basicSettings(params) {
    return post('ColdHeatSources/BasicSettings', params, true)
  },
}
//
export default coldHeatSources
