<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="project"
    pageAction="RunPage"
    :rowSelect="false"
    ref="childRef"
  >
    <template #custom-projectAddress="{ record }">
      {{
        record.province + record.city + record.district + record.detailedAddress
      }}
    </template>
    <template #custom-online="{ record }">
      <span v-if="record.online">
        <a-badge status="processing" color="#52c41a" />
        <span class="text-color-success">在线</span>
      </span>
      <span v-else>
        <a-badge status="error" />
        <span class="text-color-error">离线</span>
      </span>
    </template>
    <template #custom-operation="{ record }">
      <a @click="projectOperationlog(record)">操作日志</a>
      <a-divider type="vertical" />
      <a @click="empower(record)">异常日志</a>
    </template>
  </form-table>

  <!-- 操作日志 -->

  <a-modal
    v-model:open="operationLogOpen"
    title="操作日志"
    width="70%"
    wrap-class-name="full-modal"
    :destroyOnClose="true"
  >
    <ProjectOperationLogPage :recordwhere="recordwhere" ref="childPageRef">
    </ProjectOperationLogPage>
  </a-modal>
</template>
<script setup>
import { ref, reactive } from 'vue'
import FormTable from '../../components/FormTable.vue'
import ProjectOperationLogPage from '../../components/pages/log/ProjectOperationLogPage.vue'
const formState = ref({
  projectName: { label: '项目名称', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '网络状态',
    key: 'online',
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
  },
  {
    title: '建筑面积',
    dataIndex: 'totalArea',
    key: 'totalArea',
  },
  {
    title: '隶属小区',
    dataIndex: 'communityName',
    key: 'communityName',
  },
  {
    title: '项目地址',
    dataIndex: 'projectAddress',
    key: 'projectAddress',
  },

  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)
//编辑
const operationLogOpen = ref(false)
const recordwhere = ref({})
//打开操作日志
const projectOperationlog = record => {
  recordwhere.value = { projectId: record.id }
  operationLogOpen.value = true
}
</script>
<style>
.full-modal1 {
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(90vh);
    overflow-y: auto;
  }
  .ant-modal-body {
    flex: 1;
  }
}
</style>
