import { post, get } from '@/api/request'

const project = {
  //根据id获取信息
  get(params) {
    return get('project/get', params)
  },
  //根据id获取详情
  detail(params) {
    return get('project/detail', params)
  },
  //新增
  add(params) {
    return post('project/add', params, true)
  },
  //更新
  update(params) {
    return post('project/update', params, true)
  },
  //修改项目的Uid
  updateUid(params) {
    return get('project/updateUid', params, true)
  },
  //保存
  save(params) {
    return post('project/save', params)
  },
  //获取经销商信息
  getDealerList() {
    return get('project/getDealerList')
  },
  //修改项目项目进度
  updateProjectProgress(params) {
    return get('project/updateProjectProgress', params)
  },
  //下载配置文件
  downloadConfig(params) {
    return get('project/downloadConfig', params, true)
  },
  //下载配置文件
  uploadConfigFile(params) {
    return post('project/uploadConfigFile', params, true)
  },
  DescribeDeviceOl(params) {
    return get('project/DescribeDeviceOl', params)
  },
}
//
export default project
