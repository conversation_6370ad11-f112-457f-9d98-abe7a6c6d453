<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="quartz"
    :ftableEdit="true"
    :ftableAdd="true"
    @edit="edit"
    ref="childRef"
  >
    <template #custom-status="{ record }">
      <span class="" v-if="record.status == 0">暂停</span>
      <span class="text-color-primary" v-if="record.status == 1">开启</span>
      <span class="text-color-success" v-if="record.status == 2">运行中</span>
    </template>
    <template #custom-taskType="{ record }">
      <span class="" v-if="record.taskType == 0">DLL</span>
      <span class="" v-if="record.taskType == 1">API</span>
    </template>
    <template #custom-operation="{ record }">
      <a-divider type="vertical" />
      <a @click="deleteTask(record)">删除</a>
      <a-divider type="vertical" />
      <a-dropdown class="">
        <a @click.prevent> 更多 <DownOutlined /></a>
        <template #overlay>
          <a-menu @click="e => moreClick(e, record.id)">
            <a-menu-item key="1"> 恢复任务 </a-menu-item>
            <a-menu-item key="2"> 暂停任务 </a-menu-item>
            <a-menu-item key="3"> 立即执行 </a-menu-item>
            <a-menu-item key="4"> 执行记录 </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </template>
  </form-table>
  <!-- 新增修改 -->
  <QuartzEdit
    :open="editopen"
    @close="editopen = false"
    @updateData="refreshData"
    ref="editRef"
  >
  </QuartzEdit>

  <a-modal
    v-model:open="quartzLogopen"
    width="60%"
    wrap-class-name="full-modal"
    title="执行记录"
    :footer="null"
    :destroyOnClose="true"
  >
    <form-table
      :columns="columns1"
      modulePath="quartz"
      pageAction="logPage"
      :rowSelect="false"
      :where="recordwhere"
    >
      <template #custom-status="{ record }">
        <span class="text-color-success" v-if="record.status">成功</span>
        <span class="text-color-error" v-else>失败</span>
      </template>
    </form-table>
  </a-modal>
</template>
<script setup>
import { ref, reactive, toRaw, inject } from 'vue'
import FormTable from '../../components/FormTable.vue'
import QuartzEdit from '../../components/edit/QuartzEdit.vue'
import quartz from '@/api/methods/quartz'
import { Modal, message } from 'ant-design-vue'
// 注入父组件的方法
const menuinit = inject('menuinit')
const formState = ref({
  name: { label: '任务', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '任务',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '分组',
    dataIndex: 'jobGroup',
    key: 'jobGroup',
  },
  {
    title: 'Cron',
    dataIndex: 'cron',
    key: 'cron',
  },
  {
    title: '任务类型',
    dataIndex: 'taskType',
    key: 'taskType',
  },
  {
    title: '运行状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '最后一次运行时间',
    dataIndex: 'lastRunTime',
    key: 'lastRunTime',
  },
  {
    title: '任务描述',
    key: 'remark',
    dataIndex: 'remark',
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])

const recordwhere = ref({})
const quartzLogopen = ref(false)
// 表格更多点击事件
const moreClick = (e, id) => {
  switch (e.key) {
    case '1':
      quartz.resumeTask({ id }).then(() => {
        message.success('成功', 1, () => {
          childRef.value.tableLoad()
        })
      })
      break
    case '2':
      quartz.pauseTask({ id }).then(() => {
        message.success('成功', 1, () => {
          childRef.value.tableLoad()
        })
      })
      break
    case '3':
      quartz.triggerTask({ id }).then(() => {
        message.success('成功', 1, () => {
          childRef.value.tableLoad()
        })
      })
      break
    case '4':
      recordwhere.value = { quartzTaskId: id }
      quartzLogopen.value = true
      break
    default:
      break
  }
}
const deleteTask = record => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除该任务吗？',
    onOk() {
      quartzTask.deleteTask({ id: record.id }).then(() => {
        message.success({
          content: '成功',
          duration: 1,
          onClose: () => {
            childRef.value.tableLoad()
          },
        })
      })
    },
  })
}

//编辑
const editopen = ref(false)
const editRef = ref(null)
const edit = record => {
  editRef.value.init(record.id)
  editopen.value = true
}
const childRef = ref(null)
const refreshData = () => {
  childRef.value.tableLoad()
}
const columns1 = ref([
  {
    title: '序号',
    key: 'num',
  },
  {
    title: '任务名称',
    dataIndex: 'quartzTaskName',
    key: 'quartzTaskName',
  },

  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '结果',
    dataIndex: 'result',
    key: 'result',
  },
  {
    title: '异常信息',
    dataIndex: 'error',
    key: 'error',
  },
  {
    title: '开始时间',
    dataIndex: 'beginDate',
    key: 'beginDate',
  },
  {
    title: '结束时间',
    dataIndex: 'endDate',
    key: 'endDate',
  },
])
</script>

<style></style>
