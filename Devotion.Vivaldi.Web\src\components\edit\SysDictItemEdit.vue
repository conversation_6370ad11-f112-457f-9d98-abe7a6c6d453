<template>
  <a-drawer
    :title="editTitle"
    :width="540"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-form-item
        label="字典标签"
        name="label"
        :rules="[{ required: true, message: '字典标签不能为空' }]"
      >
        <a-input
          :class="formInfo.colorClass"
          v-model:value="formInfo.label"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="字典值" name="value">
        <a-input-number v-model:value="formInfo.value" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="颜色样式" name="colorClass">
        <a-select v-model:value="formInfo.colorClass" @change="selectColor">
          <a-select-option value="default">default</a-select-option>
          <a-select-option value="text-color-primary">primary</a-select-option>
          <a-select-option value="text-color-success">success</a-select-option>
          <a-select-option value="text-color-warning">warning</a-select-option>
          <a-select-option value="text-color-error">error</a-select-option>
          <a-select-option value="text-color-gray">gray</a-select-option>
          <a-select-option value="text-color-textgray"
            >textgray</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item label="排序" name="sort">
        <a-input-number v-model:value="formInfo.sort" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="是否启用" name="isEnable">
        <a-switch v-model:checked="formInfo.isEnable" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formInfo.remark" />
      </a-form-item>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref } from 'vue'
import { message } from 'ant-design-vue'
import sysDictItem from '@/api/methods/sysDictItem'
const props = defineProps({
  editTitle: {
    type: String,
    default: '新增',
  },
  open: {
    type: Boolean,
    required: true,
  },
  formInfo: {
    type: Object,
    required: true,
  },
})
const formRef = ref(null)
const onSave = () => {
  formRef.value.validate().then(() => {
    if (props.formInfo.id == 0) {
      sysDictItem.add(props.formInfo).then(() => {
        message.success('成功', 1)
        onClose()
        emit('updateData') // 触发事件
      })
    } else {
      sysDictItem.update(props.formInfo).then(() => {
        message.success('成功', 1)
        onClose()
        emit('updateData') // 触发事件
      })
    }
  })
}

const selectColor = e => {
  props.formInfo.colorClass = e
}

// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close'])
const onClose = () => {
  emit('close')
}
</script>
