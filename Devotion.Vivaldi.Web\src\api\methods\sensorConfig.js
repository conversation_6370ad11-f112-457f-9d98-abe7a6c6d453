import { post, get } from '@/api/request'

const sensorConfig = {
  getList(params) {
    return get('sensorConfig/getList', params)
  },
  //根据id获取信息
  get(params) {
    return get('sensorConfig/get', params)
  },
  //新增
  add(params) {
    return post('sensorConfig/add', params, true)
  },
  //更新
  update(params) {
    return post('sensorConfig/update', params, true)
  },
  getOptionsList(params) {
    return get('sensorConfig/getOptionsList', params)
  },
}
//
export default sensorConfig
