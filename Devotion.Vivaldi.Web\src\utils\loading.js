// utils/loading.js
import { createApp, h } from 'vue'
import { Spin } from 'ant-design-vue'
import { LoadingOutlined } from '@ant-design/icons-vue'
import FullscreenLoading from '@/components/FullscreenLoading.vue'

let loadingInstance = null
let loadingCount = 0

const createLoadingApp = (options) => {
  const app = createApp({
    render: () => h(FullscreenLoading, {
      ...options,
      visible: true
    })
  })

  // 注册必要组件
  app.component('ASpin', Spin)
  app.component('LoadingOutlined', LoadingOutlined)

  return app
}

export default {
  show(options = {}) {
    if (!loadingInstance) {
      const fragment = document.createElement('div')
      const app = createLoadingApp(options)
      loadingInstance = app.mount(fragment)
      document.body.appendChild(loadingInstance.$el)
    }
    loadingCount++
    document.body.style.overflow = 'hidden'
  },

  hide() {
    if (loadingCount > 0) loadingCount--
    if (loadingCount === 0 && loadingInstance) {
      loadingInstance.$destroy()
      loadingInstance.$el.remove()
      loadingInstance = null
      document.body.style.overflow = ''
    }
  }
}
