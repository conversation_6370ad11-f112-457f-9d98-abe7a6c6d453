import { post, get } from '@/api/request'

const sysDictItem = {
  //根据id获取信息
  get(params) {
    return get('sysDictItem/get', params)
  },
  //保存
  save(params) {
    return post('sysDictItem/save', params)
  },
  //新增
  add(params) {
    return post('sysDictItem/add', params)
  },
  //更新
  update(params) {
    return post('sysDictItem/update', params)
  },

  //修改是否启用
  updateIsEnabled(params) {
    return get('sysDictItem/updateIsEnabled', params)
  },
}
// 获取字典分页信息
export default sysDictItem
