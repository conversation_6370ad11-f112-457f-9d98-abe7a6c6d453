<template>
  <a-drawer
    :title="editTitle"
    width="50%"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item
            label="项目名称"
            name="projectName"
            :rules="[{ required: true, message: '项目名称不能为空' }]"
          >
            <a-input
              v-model:value="formInfo.projectName"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label="项目进度"
            name="projectProgress"
            :rules="[{ required: true, message: '请选择项目进度' }]"
          >
            <a-select
              v-model:value="formInfo.projectProgress"
              placeholder="请选择"
              :options="projectProgressdata"
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="是否归属小区">
            <a-radio-group v-model:value="formInfo.isCommunity">
              <a-radio :value="false">否</a-radio>
              <a-radio :value="true">是</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row v-if="formInfo.isCommunity">
        <a-col :span="24">
          <a-form-item
            label="选择小区"
            name="communityId"
            :rules="[{ required: true, message: '请选择选择小区' }]"
          >
            <a-select
              v-model:value="formInfo.communityId"
              placeholder="请选择"
              :options="communityList"
              @change="communityChange"
            >
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            label="省市区"
            name="provincecitydistrict"
            :rules="[{ required: true, message: '请选择省市区' }]"
          >
            <a-cascader
              v-model:value="formInfo.provincecitydistrict"
              :options="alllist"
              placeholder="请选择"
              :disabled="formInfo.isCommunity"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="详细地址"
            name="detailedAddress"
            :rules="[{ required: true, message: '详细地址不能为空' }]"
          >
            <a-input
              v-model:value="formInfo.detailedAddress"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item
            label="业主姓名"
            name="ownerName"
            :rules="[{ required: true, message: '业主姓名不能为空' }]"
          >
            <a-input v-model:value="formInfo.ownerName" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label="业主电话"
            name="ownerPhone"
            :rules="[{ required: true, message: '业主电话不能为空' }]"
          >
            <a-input v-model:value="formInfo.ownerPhone" placeholder="请输入" />
          </a-form-item>
        </a-col>
        <!-- <a-col :span="8">
          <a-form-item label="业主邮箱" name="ownerEmail">
            <a-input v-model:value="formInfo.ownerEmail" placeholder="请输入" />
          </a-form-item>
        </a-col> -->
      </a-row>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="总面积（单位：平米）" name="totalArea">
            <a-input-number
              v-model:value="formInfo.totalArea"
              placeholder="请输入"
              style="width: 100%"
              :min="0"
              :step="0.01"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="楼层数" name="floor">
            <a-input-number
              style="width: 100%"
              :min="0"
              v-model:value="formInfo.floor"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="房间数" name="numberRooms">
            <a-input-number
              style="width: 100%"
              :min="0"
              v-model:value="formInfo.numberRooms"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="8" v-if="userInfo.roleId != 4">
          <a-form-item
            label="经销商"
            name="dealerId"
            :rules="[{ required: false, message: '请选择经销商' }]"
          >
            <a-select
              v-model:value="formInfo.dealerId"
              placeholder="请选择"
              :options="dealerList"
            >
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label="开工时间"
            name="startTime"
            :rules="[{ required: true, message: '开工时间不能为空' }]"
          >
            <a-date-picker
              style="width: 100%"
              v-model:value="formInfo.startTime"
              value-format="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item
            label="竣工时间"
            name="completionTime"
            :rules="[{ required: true, message: '竣工时间不能为空' }]"
          >
            <a-date-picker
              style="width: 100%"
              v-model:value="formInfo.completionTime"
              value-format="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="工程经理" name="engineeringManager">
            <a-input
              v-model:value="formInfo.engineeringManager"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="工程设计" name="engineeringDesign">
            <a-input
              v-model:value="formInfo.engineeringDesign"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <!-- <a-col :span="8">
          <a-form-item label="工程调试" name="engineeringDebug">
            <a-input
              v-model:value="formInfo.engineeringDebug"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col> -->
      </a-row>
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="工程售后" name="engineeringAftersales">
            <a-input
              v-model:value="formInfo.engineeringAftersales"
              placeholder="请输入"
            />
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="竣工图纸" name="engineeringDrawings">
            <a-upload
              v-model:file-list="fileList"
              name="file"
              :action="action"
              @change="handleuploadChange"
            >
              <a-button>
                <upload-outlined></upload-outlined>
                上传
              </a-button>
            </a-upload>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref, reactive,inject } from 'vue'
import community from '@/api/methods/community'
import project from '@/api/methods/project'
import { dictStore } from '@/stores/dict'
import area from '@/utils/area'
import dayjs from 'dayjs'
import { message } from 'ant-design-vue'
const userInfo = inject('userInfo')
const alllist = ref(area.transformAreaData())
const dict = dictStore()
var projectProgressdata = dict.getDictItems('projectProgress')
const communityList = ref([])
const communitys = ref([])
const dealerList = ref([])
const action = ref(
  'https://iot.gzvivaldi.com/iot/api/Project/UpdateEngineeringDrawings',
)
const fileList = ref([])
community.getList().then(res => {
  communitys.value = res.data
  var items = [
    {
      label: '请选择',
      value: 0,
    },
  ]
  res.data.forEach(item => {
    items.push({
      label: item.communityName,
      value: item.id,
    })
  })
  communityList.value = items
})

project.getDealerList().then(res => {
  var items = [
    {
      label: '暂无',
      value: 0,
    },
  ]
  res.data.forEach(item => {
    items.push({
      label: item.dealerName,
      value: item.id,
    })
  })
  dealerList.value = items
})

const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
})

//表单字段的默认值
const defaultformInfo = {
  id: 0,
  projectName: '',
  houseType: null,
  provincecitydistrict: null,
  province: '',
  city: '',
  district: '',
  detailedAddress: '',
  isCommunity: false,
  communityId: null,
  projectProgress: null,
  ownerName: '',
  ownerPhone: '',
  ownerEmail: '',
  totalArea: 0,
  floor: 0,
  numberRooms: 0,
  dealerId: 0,
  startTime: '',
  completionTime: '',
  engineeringManager: '',
  engineeringDesign: '',
  engineeringDebug: '',
  engineeringAftersales: '',
  engineeringDrawings: '',
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editTitle = ref('新增')
const init = id => {
  fileList.value = []
  if (id) {
    editTitle.value = '修改'
    project.get({ id }).then(res => {
      Object.keys(res.data).forEach(key => {
        if (key in formInfo) {
          if (key == 'startTime' || key == 'completionTime') {
            if (res.data[key] !== null) {
              formInfo[key] = dayjs(res.data[key], 'YYYY-MM-DD')
            } else {
              formInfo[key] = res.data[key]
            }
          } else {
            formInfo[key] = res.data[key]
          }
        }
      })

      if (
        res.data.engineeringDrawings != '' &&
        res.data.engineeringDrawings != null
      ) {
        var engineeringDrawings = JSON.parse(res.data.engineeringDrawings)
        engineeringDrawings.forEach(item => {
          fileList.value.push({
            name: item.name,
            url: item.url,
          })
        })
      }
      formInfo.provincecitydistrict = [
        formInfo.province,
        formInfo.city,
        formInfo.district,
      ]
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
  }
}
const communityChange = (value, option) => {
  var community = communitys.value.filter(x => x.id == value)
  var provincecitydistrict = [
    community[0].province,
    community[0].city,
    community[0].district,
  ]
  formInfo.provincecitydistrict = provincecitydistrict
  formInfo.detailedAddress = community[0].detailedAddress
}

const formRef = ref(null)
const formatDate = input => dayjs(input).format('YYYY-MM-DD')
const onSave = () => {
  formRef.value.validate().then(() => {
    formInfo.dealerId ||= 0
    formInfo.communityId ||= 0
    formInfo.engineeringDrawings = JSON.stringify(fileList.value)
    formInfo.startTime = formatDate(formInfo.startTime)
    formInfo.completionTime = formatDate(formInfo.completionTime)

    if(userInfo.value.roleId==4){
      formInfo.dealerId = userInfo.value.dealerId;
    }

    if (formInfo.id == 0) {
      project.add(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    } else {
      project.update(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    }
  })
}
const handleuploadChange = info => {
  let resFileList = [...info.fileList]

  // 1. Limit the number of uploaded files
  //    Only to show two recent uploaded files, and old ones will be replaced by the new
  resFileList = resFileList.slice(-2)

  // 2. read from response and show file link
  resFileList = resFileList.map(file => {
    if (file.response) {
      // Component will show file.url as link
      file.url = file.response.data.url
      file.name = file.response.data.name
    }
    return file
  })
  fileList.value = resFileList

  // if (info.file.status !== 'uploading') {
  //   // console.log(info.file, info.fileList)
  // }
  // if (info.file.status === 'done') {
  //   fileList.value.push({
  //     name: info.file.response.data.name,
  //     status: 'done',
  //     url: info.file.response.data.url,
  //   })
  // } else if (info.file.status === 'error') {
  //   message.error(`${info.file.name} file upload failed.`)
  // }
  // var file = formInfo.engineeringDrawings
  // file.push(info.file.response.data)
}
const handleChange = () => {}
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close', 'updateData'])
const onClose = () => {
  emit('close')
}
// 使用 defineExpose 暴露方法
defineExpose({
  init,
})
</script>
