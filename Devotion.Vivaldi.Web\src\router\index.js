import { createRouter, createWebHistory } from 'vue-router'
import home from '../views/home.vue'
import { useUserStore } from '@/stores/user'
// import { menuStore } from '@/store/menu';

const routes = [
  {
    path: '/login',
    component: () => import('../views/login.vue'),
    meta: { title: '登录', cache: false },
  },
  {
    path: '/',
    name: 'home',
    component: home,
    meta: { title: '首页', cache: true }, // 首页标题
  },
  {
    path: '/',
    component: home,
    children: [
      {
        path: '/console',
        name: 'console',
        component: () => import('../views/console.vue'),
        meta: { title: '主页', cache: true },
      },
    ],
  },
  {
    path: '/',
    component: home,
    children: [
      {
        path: 'notFound',
        component: () => import('../views/notFound.vue'),
      },
    ],
  },
  {
    path: '/',
    component: home,
    name: 'configMain',
    children: [
      {
        path: '/projectConfig/configMain',
        component: () => import('../views/projectConfig/configMain.vue'),
        meta: { cache: false },
      },
    ],
  },
  {
    path: '/',
    component: home,
    name: 'settingMain',
    children: [
      {
        path: '/projectSettings/settingMain',
        component: () => import('../views/projectSettings/settingMain.vue'),
        meta: { cache: false },
      },
    ],
  },
]
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL), // 使用 history 模式
  routes,
})
// 设置全局前置导航守卫
router.beforeEach((to, from, next) => {
  document.title = to.meta.title || 'VIVALDI'
  const user = useUserStore()
  // 如果用户未登录，跳转到登录页
  if (!user.token && to.path !== '/login') {
    next('/login')
    return
  }

  // 如果路由未匹配到任何路径，跳转到 404 页面
  if (to.matched.length === 0) {
    next('/notFound')
    return
  }

  // 如果访问根路径，重定向到 /console
  if (to.path === '/') {
    next('/console')
    return
  }

  // 其他情况直接放行
  next()
})
export default router
