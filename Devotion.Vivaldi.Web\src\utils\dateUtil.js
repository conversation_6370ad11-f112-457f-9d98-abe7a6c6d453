﻿class DateUtil {
  /**
   * 获取当前时间的完整日期和时间
   * @returns {Date} 当前时间的 Date 对象
   */
  static getNow() {
    return new Date()
  }

  /**
   * 获取当前日期的年月日（格式：YYYY-MM-DD）
   * @returns {string} 格式化的日期字符串
   */
  static getDate() {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0') // 补零
    const day = String(now.getDate()).padStart(2, '0') // 补零
    return `${year}-${month}-${day}`
  }

  /**
   * 获取当前日期的年月（格式：YYYY-MM）
   * @returns {string} 格式化的年月字符串
   */
  static getYearMonth() {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0') // 补零
    return `${year}-${month}`
  }

  /**
   * 获取当前时间（格式：HH:MM:SS）
   * @returns {string} 格式化的时间字符串
   */
  static getTime() {
    const now = new Date()
    const hours = String(now.getHours()).padStart(2, '0') // 补零
    const minutes = String(now.getMinutes()).padStart(2, '0') // 补零
    const seconds = String(now.getSeconds()).padStart(2, '0') // 补零
    return `${hours}:${minutes}:${seconds}`
  }

  /**
   * 获取完整的日期和时间（格式：YYYY-MM-DD HH:MM:SS）
   * @returns {string} 格式化的日期和时间字符串
   */
  static getDateTime() {
    return `${this.getDate()} ${this.getTime()}`
  }

  /**
   * 获取 ISO 格式的日期和时间
   * @returns {string} ISO 格式的日期和时间字符串
   */
  static getISODateTime() {
    return new Date().toISOString()
  }

  /**
   * 格式化日期
   * @param {Date} date - 需要格式化的日期对象
   * @param {string} format - 格式字符串（例如：YYYY-MM-DD HH:MM:SS）
   * @returns {string} 格式化后的日期字符串
   */
  static formatDate(date, format = 'YYYY-MM-DD HH:MM:SS') {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('MM', minutes)
      .replace('SS', seconds)
  }
}

export default DateUtil
