<template>
  <div>
    <div class="table-search" v-if="formState">
      <a-form
        name="advanced_search"
        ref="tableformRef"
        :model="formState"
        @finish="onFinish"
      >
        <a-row :gutter="24">
          <template v-for="(v, key, index) in formState" :key="key">
            <a-col v-show="state.expand || index < 8" :span="6">
              <a-form-item :name="`${key}`" :label="`${v.label}`">
                <a-input
                  v-if="v.type === 'text'"
                  v-model:value="v.value"
                  placeholder="请输入"
                />
                <a-select
                  v-if="v.type === 'select'"
                  v-model:value="v.value"
                  placeholder="请选择"
                  :options="v.data"
                  showSearch
                />
                <a-radio-group
                  v-if="v.type === 'radio'"
                  v-model:value="v.value"
                  :options="v.data"
                >
                </a-radio-group>
                <a-range-picker
                  v-if="v.type === 'time'"
                  v-model:value="v.value"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </a-form-item>
            </a-col>
          </template>
          <a-col
            :span="forminputbuttonspan"
            style="text-align: right; margin-bottom: 24px"
          >
            <a-space>
              <a-button type="primary" html-type="submit">搜索</a-button>
              <a-button style="margin: 0 8px" @click="resetForm">重置</a-button>
              <a
                style="font-size: 12px"
                v-if="formStatelength > 8"
                @click="state.expand = !state.expand"
              >
                <template v-if="state.expand"> <UpOutlined />收起 </template>
                <template v-else> <DownOutlined />展开 </template>
              </a>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <div class="card-table">
      <a-table
        :columns="filteredColumns"
        :dataSource="state.dataSource"
        :pagination="page ? pagination : false"
        :rowSelection="rowSelect ? rowSelection : null"
        :loading="state.loading"
        rowKey="id"
        :size="formConfig.tableSize"
        :bordered="formConfig.tableBordered"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
        @resizeColumn="handleResizeColumn"
      >
        <!-- 插槽部分 -->
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'num'">
            <span>{{
              (pagination.current - 1) * pagination.pageSize + index + 1
            }}</span>
          </template>
          <template v-if="column.key === 'operation'">
            <!-- 插槽内容 -->
            <a v-if="ftableDetails" @click="details(record)">详情</a>
            <a-divider
              v-if="ftableDetails && (ftableEdit || ftableDelete)"
              type="vertical"
            />
            <a v-if="ftableEdit" @click="edit(record)">修改</a>
            <a-divider v-if="ftableEdit && ftableDelete" type="vertical" />
            <a-popconfirm
              title="确定要删除该数据吗?"
              @confirm="tabelDelete(record)"
            >
              <a v-if="ftableDelete">删除</a>
            </a-popconfirm>
          </template>
          <!-- 如果父组件传递了对应列的插槽，则渲染插槽内容，否则默认展示数据 -->
          <slot
            :name="`custom-${column.key}`"
            :record="record"
            :column="column"
          >
            {{ record[column.dataIndex] }}
          </slot>
        </template>
        <template #title>
          <div class="flex-row">
            <div class="flex-item text-align-left">
              <a-space>
                <a-button
                  type="primary"
                  v-if="ftableAdd"
                  @click="edit"
                  size="middle"
                >
                  <PlusOutlined />新增
                </a-button>
                <a-button
                  type="primary"
                  v-if="ftableDelete"
                  @click="batchDelete"
                  :disabled="!hasSelected"
                  danger
                  size="middle"
                >
                  <DeleteOutlined />删除
                </a-button>
                <slot :name="'table-toolbar'"></slot>
              </a-space>
            </div>
            <div class="flex-item text-align-right padding-right-10">
              <a-space size="large">
                <a class="text-color-black" @click.prevent @click="tableLoad">
                  <RedoOutlined />
                </a>
                <a-dropdown class="">
                  <a class="text-color-black" @click.prevent>
                    <ColumnHeightOutlined />
                  </a>
                  <template #overlay>
                    <a-menu @click="dropdownonTableSizeClick">
                      <a-menu-item key="large"> 默认 </a-menu-item>
                      <a-menu-item key="middle"> 中等 </a-menu-item>
                      <a-menu-item key="small"> 紧凑 </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
                <a-dropdown
                  :trigger="['click']"
                  :open="dropdownVisible"
                  @openChange="handleDropdownVisibility"
                >
                  <a class="text-color-black" @click.prevent>
                    <AlignCenterOutlined />
                  </a>
                  <template #overlay>
                    <a-checkbox-group
                      v-model:value="checkedColumns"
                      @change="handleColumnChange"
                    >
                      <a-menu>
                        <a-menu-item
                          v-for="col in columns"
                          :key="col.dataIndex"
                        >
                          <a-checkbox :value="col.dataIndex" @click.stop>{{
                            col.title
                          }}</a-checkbox>
                        </a-menu-item>
                      </a-menu>
                    </a-checkbox-group>
                  </template>
                </a-dropdown>
              </a-space>
            </div>
          </div>
        </template>
      </a-table>
    </div>
  </div>
</template>
<script setup>
import { ref, defineProps, defineEmits, reactive, computed } from 'vue'
import { configStore } from '@/stores/config'
import { post, get } from '@/api/request'
import { Modal, message } from 'ant-design-vue'
const config = configStore()
const formConfig = reactive({
  tableSize: config.tableSize,
  tableBordered: config.tableBordered,
})
// 定义 props
const props = defineProps({
  formState: Object, //表格上面搜索条件
  modulePath: String, //模块路径
  pageAction: {
    //分页方法名称
    type: String,
    default: 'page',
  },
  where: {
    //查询条件
    type: Array,
    default: [],
  },
  ftableEdit: {
    //编辑方法名称
    type: Boolean,
    default: false,
  },
  ftableAdd: {
    //新增方法名称
    type: Boolean,
    default: false,
  },
  ftableDelete: {
    type: Boolean,
    default: false,
  },
  ftableDeleteAction: {
    //删除方法名称
    type: String,
    default: 'delete',
  },
  ftableDetails: {
    type: Boolean,
    default: false,
  },
  method: {
    //请求方法
    type: String,
    default: 'post',
  },
  page: {
    //是否分页
    type: Boolean,
    default: true,
  },
  columns: Array, //表格列
  rowSelect: {
    //选择行
    type: Boolean,
    default: true,
  },
})
const state = reactive({
  loading: false, //表格加载中状态
  params: {}, //表单请求条件参数
  expand: false, // 表单判断是从合并展开属性
  dataSource: [], //定义表格数据源
  selectedRowKeys: [], //表格选中行
})
const formStatelength = ref(
  props.formState ? Object.keys(props.formState).length : 0,
)
const forminputbuttonspan = computed(() => {
  const values = [18, 12, 6, 24]
  if (!state.expand & (formStatelength.value > 8)) {
    return 24
  } else {
    return values[(formStatelength.value - 1) % 4]
  }
})
//表单重置属性
const tableformRef = ref(null)
//表单搜索按钮事件
const onFinish = values => {
  pagination.value.current = 1
  tableLoad()
}
//重置表单
const resetForm = () => {
  Object.keys(props.formState).forEach(key => {
    if (props.formState[key].type === 'time') {
      props.formState[key].value = '' // 重置时间范围
    } else if (props.formState[key].type === 'select') {
      props.formState[key].value = null // 重置选择框
    } else {
      props.formState[key].value = '' // 重置输入框
    }
  })
}
//列的显示/隐藏选择器
const dropdownVisible = ref(false)
const handleDropdownVisibility = visible => {
  dropdownVisible.value = visible
}
const handleColumnChange = checkedValues => {
  checkedColumns.value = checkedValues
}
const initialCheckedColumns = props.columns.map(col => col.dataIndex)
const checkedColumns = ref([...initialCheckedColumns])
const filteredColumns = computed(() =>
  props.columns.filter(col => checkedColumns.value.includes(col.dataIndex)),
)
// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: total => `共 ${total} 条`,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '30', '40'],
  showQuickJumper: true,
})
const hasSelected = computed(() => state.selectedRowKeys.length > 0)
//选择行事件
const rowSelection = {
  onChange: (selectedRowKeys, selectedRows) => {
    state.selectedRowKeys = selectedRowKeys
  },
}
// 分页和表格变化处理
const handleTableChange = newPagination => {
  pagination.value = {
    ...pagination.value,
    ...newPagination,
  }
  tableLoad()
}
//加载表格数据
const tableLoad = () => {
  var values = props.formState
  if (values) {
    const newValues = Object.keys(values).reduce((acc, key) => {
      if (key === 'createTime') {
        if (values[key].value != null && values[key].value.length != 0) {
          acc[key] = values[key].value.join('~')
          return acc
        }
      }
      acc[key] = values[key].value
      return acc
    }, {})
    state.params = newValues
  }
  state.loading = true
  var data = state.params
  Object.assign(data, props.where)
  if (props.page) {
    data.page = pagination.value.current
    data.size = pagination.value.pageSize
  }
  if (props.method == 'post') {
    post(props.modulePath + '/' + props.pageAction, data).then(res => {
      state.loading = false
      state.dataSource = res.data
      if (props.page) pagination.value.total = res.count // 计算总条数
    })
  }
  if (props.method == 'get') {
    get(props.modulePath + '/' + props.pageAction, data).then(res => {
      state.loading = false
      state.dataSource = res.data
      if (props.page) pagination.value.total = res.count // 计算总条数
    })
  }
}
tableLoad()
//表格选择size
const dropdownonTableSizeClick = ({ key }) => {
  config.tableSize = key
  formConfig.tableSize = key
}
const emit = defineEmits(['edit', 'details'])
//编辑
const edit = record => {
  // 触发自定义事件，父组件会监听这个事件
  emit('edit', record)
}
const details = record => {
  emit('details', record)
}
//批量删除
const batchDelete = () => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除这些数据吗？',
    onOk() {
      get(
        props.modulePath + '/' + props.ftableDeleteAction,
        {
          ids: state.selectedRowKeys,
        },
        true,
        '删除中...',
      ).then(() => {
        message.success({
          content: '成功',
          duration: 1,
          onClose: () => {
            state.selectedRowKeys = []
            tableLoad()
          },
        })
      })
    },
    onCancel() {},
  })
}
const tabelDelete = record => {
  get(
    props.modulePath + '/' + props.ftableDeleteAction,
    {
      ids: record.id,
    },
    true,
    '删除中...',
  ).then(() => {
    message.success({
      content: '成功',
      duration: 1,
      onClose: () => {
        tableLoad()
      },
    })
  })
}
const handleResizeColumn = (w, col) => {
  col.width = w
}
const getSelectedRows = () => state.selectedRowKeys
const getDataSource = () => state.dataSource
// 将配置和方法暴露给父组件
defineExpose({
  edit,
  tableLoad,
  getSelectedRows,
  getDataSource,
})
</script>
<style scoped>
.table-search {
  margin-bottom: 8px;
  padding: 24px 24px 0px;
  background-color: white;
  border-radius: 3px;
}
.card-table {
  padding: 24px;
  background-color: white;
  border-radius: 3px;
}
.table-operations {
  margin-bottom: 16px;
}
.table-operations > button {
  margin-right: 8px;
}
</style>
