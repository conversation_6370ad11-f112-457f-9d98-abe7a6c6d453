<template>
  <a-row :gutter="16">
    <a-col :span="12">
      <a-card :bordered="false" :loading="loading">
        <template #title
          ><DesktopOutlined :style="{ color: '#1677ff' }" /> CPU</template
        >
        <div class="flex-row text-font-bold">
          <div class="flex-item">属性</div>
          <div class="flex-item">值</div>
        </div>
        <hr />
        <div class="flex-row">
          <div class="flex-item">核心数</div>
          <div class="flex-item">{{ data.processorCount }}</div>
        </div>
        <hr />
        <div class="flex-row">
          <div class="flex-item">用户使用率</div>
          <div class="flex-item">{{ data.processorCount }}</div>
        </div>
        <hr />
        <div class="flex-row">
          <div class="flex-item">系统使用率</div>
          <div class="flex-item">{{ data.cpuLoad }}</div>
        </div>
        <hr />
        <div class="flex-row">
          <div class="flex-item">当前空闲率</div>
          <div class="flex-item">{{ data.cpuSurplusLoad }}</div>
        </div>
      </a-card>
    </a-col>
    <a-col :span="12">
      <a-card :bordered="false" :loading="loading">
        <template #title
          ><DatabaseOutlined :style="{ color: '#1677ff' }" /> 内存</template
        >
        <div class="flex-row text-font-bold">
          <div class="flex-item">属性</div>
          <div class="flex-item">值</div>
        </div>
        <hr />
        <div class="flex-row">
          <div class="flex-item">总内存</div>
          <div class="flex-item">{{ data.physicalMemory }}</div>
        </div>
        <hr />
        <div class="flex-row">
          <div class="flex-item">已用内存</div>
          <div class="flex-item">{{ data.memoryUsed }}</div>
        </div>
        <hr />
        <div class="flex-row">
          <div class="flex-item">剩余内存</div>
          <div class="flex-item">{{ data.memoryAvailable }}</div>
        </div>
        <hr />
        <div class="flex-row">
          <div class="flex-item">使用率</div>
          <div class="flex-item">{{ data.memoryUsage }}</div>
        </div>
      </a-card>
    </a-col>
  </a-row>
  <a-card :bordered="false" class="margin-top-10" :loading="loading">
    <template #title
      ><DesktopOutlined :style="{ color: '#1677ff' }" /> 服务器信息</template
    >
    <div class="flex-row text-font-bold">
      <div class="flex-item">服务器名称</div>
      <div class="flex-item">服务器 IP</div>
      <div class="flex-item">操作系统</div>
      <div class="flex-item">系统架构</div>
    </div>
    <hr />
    <div class="flex-row">
      <div class="flex-item">{{ data.hostName }}</div>
      <div class="flex-item">{{ data.stringIP }}</div>
      <div class="flex-item">{{ data.osDescription }}</div>
      <div class="flex-item">{{ data.osArchitecture }}</div>
    </div>
  </a-card>
  <a-card :bordered="false" class="margin-top-10" :loading="loading">
    <template #title
      ><DesktopOutlined :style="{ color: '#1677ff' }" /> 磁盘状态</template
    >
    <div class="flex-row text-font-bold">
      <div class="flex-item">盘符</div>
      <div class="flex-item">总大小</div>
      <div class="flex-item">已用大小</div>
      <div class="flex-item">可用大小</div>
      <div class="flex-item">已用百分比</div>
    </div>
    <hr />
    <template v-for="(item, index) in data.disks" v-bind:key="index">
      <div class="flex-row">
        <div class="flex-item">{{ item.driveletter }}</div>
        <div class="flex-item">{{ item.totalsize }}</div>
        <div class="flex-item">{{ item.usedSize }}</div>
        <div class="flex-item">{{ item.availableSize }}</div>
        <div class="flex-item">{{ item.percentSize }}</div>
      </div>
      <hr />
    </template>
  </a-card>
</template>
<script setup>
import system from '@/api/methods/system'
import { ref } from 'vue'
const loading = ref(true)
const data = ref({})
const init = () => {
  system.getSystemDescription().then(res => {
    data.value = res.data

    loading.value = !loading.value
  })
}
init()
</script>
<style scoped>
hr {
  height: 0;
  line-height: 0;
  margin: 10px 0;
  padding: 0;
  border: none;
  border-bottom: 1px solid #eee;
  clear: both;
  overflow: hidden;
  background: 0 0;
}
</style>
