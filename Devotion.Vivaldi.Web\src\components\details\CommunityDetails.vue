<template>
  <!-- 详情 -->
  <a-modal
    :open="open"
    title="详情"
    width="60%"
    wrap-class-name="full-modal"
    @cancel="onClose"
    :destroyOnClose="true"
  >
    <a-descriptions title="" bordered :column="2">
      <a-descriptions-item label="小区名称" span="2">
        {{ info.communityName }}
      </a-descriptions-item>
      <a-descriptions-item label="房屋类型" span="2">
        <span
          v-html="dictTemplate.tabletempInt('houseType', info.houseType)"
        ></span>
      </a-descriptions-item>
      <a-descriptions-item label="省">
        {{ info.province }}
      </a-descriptions-item>
      <a-descriptions-item label="市">
        {{ info.city }}
      </a-descriptions-item>
      <a-descriptions-item label="区" span="2"
        >{{ info.district }}
      </a-descriptions-item>
      <a-descriptions-item label="详细地址" span="2">
        {{ info.detailedAddress }}
      </a-descriptions-item>
      <a-descriptions-item label="联系人">
        {{ info.contactPerson }}
      </a-descriptions-item>
      <a-descriptions-item label="联系电话">
        {{ info.contactPhone }}
      </a-descriptions-item>
      <a-descriptions-item label="联系邮箱" span="2">
        {{ info.contactEmail }}
      </a-descriptions-item>
      <a-descriptions-item label="创建人">
        {{ info.accountName }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间">
        {{ info.createTime }}
      </a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button key="submit" type="primary" @click="onClose">关闭</a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { defineProps, defineEmits } from 'vue'
import dictTemplate from '@/utils/dictTemplate'
const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
  info: {
    type: Object,
    required: true,
  },
})
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close'])
const onClose = () => {
  emit('close')
}
</script>
