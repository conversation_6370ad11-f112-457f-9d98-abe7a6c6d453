<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="sysMenu"
    pageAction="MenuTreeList"
    method="get"
    :page="false"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    @edit="edit"
    ref="childRef"
  >
    <!-- 可以通过插槽自定义单元格 -->
    <template #custom-isEnable="{ record }">
      <a-switch
        v-model:checked="record.isEnable"
        checked-children="是"
        un-checked-children="否"
        @change="handleSwitchChange(record)"
      />
    </template>
    <template #custom-cache="{ record }">
      <a-switch
        v-model:checked="record.cache"
        checked-children="是"
        un-checked-children="否"
        @change="handleSwitchChange(record)"
      />
    </template>
    <template #custom-type="{ record }">
      <span v-if="record.type == 1">目录</span>
      <span v-else>菜单</span>
    </template>
    <template #custom-operation="{ record }">
      <a-button
        v-if="record.type == 1"
        type="link"
        size="small"
        @click="addSubmenu(record)"
        >新增子菜单</a-button
      >
    </template>
  </form-table>
  <!-- 新增修改 -->
  <a-drawer
    :title="editTitle"
    :width="540"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical" :rules="rules">
      <a-form-item label="上级目录" name="parentId">
        <a-tree-select
          v-model:value="formInfo.parentId"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择"
          allow-clear
          tree-default-expand-all
          :tree-data="menuList"
          tree-node-filter-prop="label"
          @change="selecthandleChange"
        >
        </a-tree-select>
        <!-- <a-select v-model:value="formInfo.parentId" placeholder="请选择" :options="menuList" >
              </a-select> -->
      </a-form-item>
      <a-form-item label="菜单名称" name="title">
        <a-input v-model:value="formInfo.title" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="命名路由" name="path">
        <a-input v-model:value="formInfo.path" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="类型">
        <a-radio-group v-model:value="formInfo.type">
          <a-radio :value="1">目录</a-radio>
          <a-radio :value="2">菜单</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="图标" name="icon">
        <a-input v-model:value="formInfo.icon" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="排序" name="Sort">
        <a-input-number v-model:value="formInfo.sort" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="是否启用" name="isEnable">
        <a-switch v-model:checked="formInfo.isEnable" />
      </a-form-item>
      <a-form-item label="是否开启缓存" name="cache">
        <a-switch v-model:checked="formInfo.cache" />
      </a-form-item>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { ref, reactive, toRaw, inject } from 'vue'
import FormTable from '../../components/FormTable.vue'
import sysMenu from '@/api/methods/sysMenu'
import { message } from 'ant-design-vue'
import { menuStore } from '@/stores/menu'
// 注入父组件的方法
const menuinit = inject('menuinit')
const formState = ref({
  name: { label: '菜单名', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '菜单名',
    dataIndex: 'title',
    key: 'title',
    width: 200,
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '命名路由',
    dataIndex: 'path',
    key: 'path',
  },
  {
    title: '图标',
    dataIndex: 'icon',
    key: 'icon',
  },
  {
    title: '排序',
    key: 'sort',
    dataIndex: 'sort',
  },
  {
    title: '是否启用',
    key: 'isEnable',
    dataIndex: 'isEnable',
  },
  {
    title: '是否缓存',
    key: 'cache',
    dataIndex: 'cache',
  },
  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const formInfo = reactive({
  id: '00000000-0000-0000-0000-000000000000',
  parentId: '00000000-0000-0000-0000-000000000000',
  title: '',
  path: '',
  icon: '',
  sort: 0,
  isEnable: true,
  cache: true,
  type: 1,
})
const rules = {
  title: [
    {
      required: true,
      message: '菜单名称',
      trigger: 'change',
    },
  ],
  path: [
    {
      required: true,
      message: '命名路由',
      trigger: 'change',
    },
  ],
}
const open = ref(false)
const editTitle = ref('新增')
const menuList = ref([])
const childRef = ref(null)
// 引用表单的ref
const formRef = ref(null)
//编辑
const edit = record => {
  sysMenu.getTreeSelectMenu().then(res => {
    menuList.value = res.data
    // 触发自定义事件，父组件会监听这个事件
    if (record.id) {
      editTitle.value = '修改'
      sysMenu.get({ id: record.id }).then(res => {
        Object.assign(formInfo, res.data)
      })
    } else {
      editTitle.value = '新增'
      formInfo.id = '00000000-0000-0000-0000-000000000000'
      formInfo.parentId = '00000000-0000-0000-0000-000000000000'
      formInfo.path = ''
    }
    open.value = true
  })
}
//新增子菜单
const addSubmenu = record => {
  sysMenu.getTreeSelectMenu().then(res => {
    menuList.value = res.data
    editTitle.value = '新增子菜单'
    formInfo.id = '00000000-0000-0000-0000-000000000000'
    formInfo.parentId = record.id
    formInfo.sort = record.sort
    formInfo.path = '/' + record.path + '/'
    open.value = true
  })
}

const onSave = () => {
  formRef.value
    .validate()
    .then(() => {
      sysMenu.save(toRaw(formInfo)).then(() => {
        message.success('成功', 1)
        open.value = false
        formRef.value.resetFields()
        childRef.value.tableLoad()

        const menu = menuStore()
        sysMenu
          .getmenu()
          .then(res => {
            menu.menus = res.data

            if (menuinit) {
              menuinit()
            }
          })
          .catch()
      })
    })
    .catch(error => {
      console.log('error', error)
    })
}
//修改是否启用
const handleSwitchChange = record => {
  var data = {
    id: record.id,
    isEnabled: record.isEnable,
  }
  sysMenu.updateIsEnabled(data).then(() => {
    childRef.value.tableLoad()
  })
}
const onClose = () => {
  formRef.value.resetFields()
  open.value = false
}
const selecthandleChange = value => {
  if (value == '00000000-0000-0000-0000-000000000000') {
    formInfo.path = ''
  } else {
    menuList.value.forEach(item => {
      if (item.value == value) {
        formInfo.path = '/' + item.path + '/'
      }
    })
  }
}
</script>

<style></style>
