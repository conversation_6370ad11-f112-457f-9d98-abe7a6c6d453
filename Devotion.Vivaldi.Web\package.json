{"name": "Devotion.Vivaldi.Web", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2": "^5.2.12", "alova": "^3.1.0", "ant-design-vue": "^4.2.6", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "pinia": "^2.2.4", "pinia-plugin-persist": "^1.0.0", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@eslint/js": "^9.12.0", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/eslint-config-prettier": "^10.0.0", "eslint": "^9.12.0", "eslint-plugin-vue": "^9.29.0", "less": "^4.2.2", "plop": "^4.0.1", "prettier": "^3.3.3", "vite": "^5.4.8"}}