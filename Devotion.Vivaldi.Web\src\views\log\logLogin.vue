<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="LogLogin"
    :rowSelect="false"
  >
    <!-- 可以通过插槽自定义单元格 -->
    <!-- 可以通过插槽自定义单元格 -->
    <template #custom-status="{ record }">
      <span class="text-color-success" v-if="record.status">成功</span>
      <span class="text-color-error" v-else>失败</span>
    </template>
  </form-table>
</template>
<script setup>
import { ref } from 'vue'
import FormTable from '../../components/FormTable.vue'

const formState = ref({
  userName: { label: '用户名称', value: '', type: 'text' },
  createTime: { label: '日期范围', value: '', type: 'time' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '用户名称',
    dataIndex: 'userName',
  },
  {
    title: '登录ip',
    dataIndex: 'loginIp',
  },
  {
    title: '登录地点',
    dataIndex: 'loginLocation',
  },
  {
    title: '浏览器',
    dataIndex: 'browser',
  },
  {
    title: '操作系统',
    dataIndex: 'operatingSystem',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
])
</script>

<style></style>
