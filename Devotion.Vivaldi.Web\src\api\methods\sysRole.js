import { post, get } from '@/api/request'

const sysRole = {
  //根据id获取信息
  get(params) {
    return get('sysRole/get', params)
  },
  //新增
  add(params) {
    return post('sysRole/add', params)
  },
  //更新
  update(params) {
    return post('sysRole/update', params)
  },
  //保存
  save(params) {
    return post('sysRole/save', params)
  },
  //修改是否启用
  updateIsEnabled(params) {
    return get('sysRole/updateIsEnabled', params)
  },
  //查询角色列表
  getList() {
    return get('sysRole/getList')
  },

  //获取角色关联菜单信息（树结构）
  getRoleMenu(params) {
    return get('sysRole/getRoleMenu', params)
  },
  //保存角色关联菜单
  savePower(params) {
    return post('sysRole/savePower', params)
  },
}
// 获取角色分页信息
export default sysRole
