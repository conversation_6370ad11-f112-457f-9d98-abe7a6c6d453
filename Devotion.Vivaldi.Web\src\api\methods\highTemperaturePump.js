import { post, get } from '@/api/request'

const highTemperaturePump = {
  //根据id获取信息
  get(params) {
    return get('highTemperaturePump/get', params)
  },
  getList(params) {
    return get('highTemperaturePump/getList', params)
  },
  //新增
  add(params) {
    return post('highTemperaturePump/add', params, true)
  },
  //更新
  update(params) {
    return post('highTemperaturePump/update', params, true)
  },
  //删除
  delete(params) {
    return get('highTemperaturePump/delete', params)
  },
}
//
export default highTemperaturePump
