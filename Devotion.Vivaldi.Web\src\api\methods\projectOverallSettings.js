import { post, get } from '@/api/request'

const projectOverallSettings = {
  //根据项目ID获取项目总体设置及运行信息
  getByProjectId(params) {
    return get('projectOverallSettings/getByProjectId', params)
  },
  getRedisProjectInfoByProjectId(params) {
    return get('projectOverallSettings/GetRedisProjectInfoByProjectId', params)
  },
  //修改
  update(params) {
    return post('projectOverallSettings/update', params, true)
  },

  //修改
  updateDutyTimeSetting(params) {
    return post('projectOverallSettings/UpdateDutyTimeSetting', params, true)
  },
  //同步算法统计室外温湿度
  projectTHTB(params) {
    return get('projectOverallSettings/ProjectTHTB', params)
  },
}
//
export default projectOverallSettings
