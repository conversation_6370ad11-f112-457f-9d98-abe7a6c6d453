<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="LogOperation"
    :rowSelect="false"
    :ftableDetails="true"
    @details="details"
  >
  </form-table>
  <!-- 详情 -->
  <LogOperationDetails
    :open="detailsopen"
    :info="info"
    @close="detailsopen = false"
  >
  </LogOperationDetails>
</template>
<script setup>
import { ref } from 'vue'
import FormTable from '../../components/FormTable.vue'
import LogOperationDetails from '../../components/details/logOperationDetails.vue'
import logOperation from '@/api/methods/logOperation'
const formState = ref({
  controllerName: { label: '控制器名称', value: '', type: 'text' },
  operationMethod: { label: '操作方法', value: '', type: 'text' },
  operationPersonnel: { label: '操作人员', value: '', type: 'text' },
  createTime: { label: '日期范围', value: '', type: 'time' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '控制器名称',
    dataIndex: 'controllerName',
    width: 200,
  },
  {
    title: '操作方法',
    dataIndex: 'operationMethod',
    width: 200,
  },
  {
    title: '请求地址',
    dataIndex: 'requestAddress',
    ellipsis: true,
  },
  {
    title: '请求方式',
    dataIndex: 'requestMethod',
    width: 100,
  },
  {
    title: '请求Ip',
    dataIndex: 'operationIp',
    width: 120,
  },
  {
    title: '请求端源',
    dataIndex: 'requestFrom',
    width: 100,
  },
  {
    title: 'UserAgent',
    dataIndex: 'userAgent',
    ellipsis: true,
  },
  {
    title: '操作人员',
    dataIndex: 'operationPersonnel',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 100,
  },
])
//详情
const detailsopen = ref(false)
const info = ref({})
const details = record => {
  logOperation.detail({ id: record.id }).then(res => {
    info.value = res.data
    detailsopen.value = true
  })
}
const detailsHandleCancel = () => {
  detailsopen.value = false
}
</script>
<style></style>
