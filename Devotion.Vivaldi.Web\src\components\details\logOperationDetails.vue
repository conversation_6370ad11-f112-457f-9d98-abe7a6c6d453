<template>
  <a-modal
    :open="open"
    title="详情"
    width="60%"
    wrap-class-name="full-modal"
    @cancel="onClose"
  >
    <a-descriptions title="" bordered :column="2">
      <a-descriptions-item label="控制器名称">
        {{ info.controllerName }}
      </a-descriptions-item>
      <a-descriptions-item label="请求Ip">
        {{ info.operationIp }}
      </a-descriptions-item>
      <a-descriptions-item label="操作方法">
        {{ info.operationMethod }}
      </a-descriptions-item>
      <a-descriptions-item label="请求端源"
        >{{ info.requestFrom }}
      </a-descriptions-item>
      <a-descriptions-item label="请求地址">
        {{ info.requestAddress }}
      </a-descriptions-item>
      <a-descriptions-item label="请求方式">
        {{ info.requestMethod }}
      </a-descriptions-item>
      <a-descriptions-item label="执行耗时">
        {{ info.elapsedMilliseconds }}
      </a-descriptions-item>
      <a-descriptions-item label="请求时间">
        {{ info.createTime }}
      </a-descriptions-item>
      <a-descriptions-item label="操作人员">
        {{ info.operationPersonnel }}
      </a-descriptions-item>
    </a-descriptions>
    <a-divider orientation="left" plain="true"></a-divider>
    <a-descriptions title="" :column="1" bordered>
      <a-descriptions-item label="userAgent">
        {{ info.userAgent }}
      </a-descriptions-item>
      <a-descriptions-item label="授权信息">
        {{ info.asccessToken }}
      </a-descriptions-item>
      <a-descriptions-item label="请求参数">
        {{ info.requestParameters }}
      </a-descriptions-item>
      <a-descriptions-item label="返回参数">
        {{ info.returnParameters }}
      </a-descriptions-item>
    </a-descriptions>
    <template #footer>
      <a-button key="submit" type="primary" @click="onClose">关闭</a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
  info: {
    type: Object,
    required: true,
  },
})
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close'])
const onClose = () => {
  emit('close')
}
</script>
