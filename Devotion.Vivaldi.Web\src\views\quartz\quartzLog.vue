<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="quartz"
    pageAction="logPage"
    :rowSelect="false"
  >
    <template #custom-status="{ record }">
      <span class="text-color-success" v-if="record.status">成功</span>
      <span class="text-color-error" v-else>失败</span>
    </template>
  </form-table>
</template>
<script setup>
import { ref } from 'vue'
import FormTable from '../../components/FormTable.vue'
const formState = ref({
  quartzTaskName: { label: '任务名称', value: '', type: 'text' },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
    width: 80,
  },
  {
    title: '任务名称',
    dataIndex: 'quartzTaskName',
    key: 'quartzTaskName',
  },

  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '结果',
    dataIndex: 'result',
    key: 'result',
  },
  {
    title: '异常信息',
    dataIndex: 'error',
    key: 'error',
  },
  {
    title: '开始时间',
    dataIndex: 'beginDate',
    key: 'beginDate',
    width: 200,
  },
  {
    title: '结束时间',
    dataIndex: 'endDate',
    key: 'endDate',
    width: 200,
  },
])
</script>
<style></style>
