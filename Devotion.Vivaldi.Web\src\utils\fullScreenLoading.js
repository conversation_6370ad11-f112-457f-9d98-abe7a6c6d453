// utils/fullScreenLoading.js
import { createApp, defineComponent, h } from 'vue'
import { Spin } from 'ant-design-vue'

let instance = null

function getSpinInstance(tipText = '加载中...') {
  if (!instance) {
    const SpinComponent = defineComponent({
      render() {
        return h(Spin, {
          spinning: true,
          tip: tipText,
        })
      },
    })
    const app = createApp(SpinComponent)
    instance = app.mount(document.createElement('div'))
    instance.$el.style.cssText =
      'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255, 255, 255, 0.2); z-index: 9999; display: flex; justify-content: center; align-items: center;'
    document.body.appendChild(instance.$el)
  }
  return instance
}

export default {
  show(tipText = '加载中...') {
    getSpinInstance(tipText)
  },
  hide() {
    if (instance) {
      document.body.removeChild(instance.$el)
      instance.$el.remove()
      instance = null
    }
  },
}
