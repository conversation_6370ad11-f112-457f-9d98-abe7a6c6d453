<template>
  <a-drawer
    :title="editTitle"
    :width="540"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-form-item
        label="角色名称"
        name="name"
        :rules="[{ required: true, message: '角色名称不能为空' }]"
      >
        <a-input v-model:value="formInfo.name" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="是否启用" name="isEnable">
        <a-switch v-model:checked="formInfo.isEnable" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formInfo.remark" />
      </a-form-item>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import sysRole from '@/api/methods/sysRole'
const props = defineProps({
  open: {
    type: Boolean,
    required: true,
  },
})
//表单字段的默认值
const defaultformInfo = {
  id: 0,
  name: '',
  isEnable: true,
  remark: '',
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editTitle = ref('新增')
const init = id => {
  if (id) {
    editTitle.value = '修改'
    sysRole.get({ id }).then(res => {
      Object.assign(formInfo, res.data)
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
  }
}

const formRef = ref(null)
const onSave = () => {
  formRef.value.validate().then(() => {
    if (formInfo.id == 0) {
      sysRole.add(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    } else {
      sysRole.update(formInfo).then(() => {
        message.success('成功', 1, () => {
          onClose()
          emit('updateData') // 触发事件
        })
      })
    }
  })
}
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close', 'updateData'])
const onClose = () => {
  emit('close')
}
// 使用 defineExpose 暴露方法
defineExpose({
  init,
})
</script>
