<template>
  <!--冷热源设置-->
  <a-breadcrumb style="background-color: #fff; padding: 0 0 10px 10px">
    <a-breadcrumb-item
      ><a @click="changeActive(1)">系统设置</a></a-breadcrumb-item
    >
    <a-breadcrumb-item>新风机</a-breadcrumb-item>
  </a-breadcrumb>
  <div>
    <a-card>
      <div v-if="freshAirFanlist.length > 0">
        <a-row :gutter="16">
          <a-col :span="4">
            <div
              v-for="(item, index) in freshAirFanlist"
              :key="item.id"
              class="listitem"
              :class="{ listitemselected: selectedIndex === index }"
              @click="handleItemClick(item, index)"
            >
              {{ item.freshAirFanName }}
            </div>
          </a-col>
          <a-col :span="20">
            <a-descriptions
              :title="freshAirFaninfo.freshAirFanName + '的运行信息'"
              layout="vertical"
              bordered
              :column="10"
            >
              <a-descriptions-item label="区域设置">
                <a-space wrap>
                  <a-button
                    v-if="userInfo.roleId != 4"
                    type="primary"
                    @click="
                      () => {
                        if (!userInfo.isConfig) {
                          message.error('暂无配置权限，无法进行操作')
                          return
                        }
                        settiingshow = true
                        freshAirAreaParametersshow = false
                      }
                    "
                    >设置</a-button
                  >
                  <a-button type="primary" @click="onCoolingParameters()"
                    >运行参数</a-button
                  >
                </a-space>
              </a-descriptions-item>

              <a-descriptions-item label="运行模式">
                {{ yxmodel }}
              </a-descriptions-item>
              <a-descriptions-item label="系统状态">
                {{ systatus }}
              </a-descriptions-item>
              <a-descriptions-item label="工作模式">
                <span v-if="projectOverallSettings.operatingMode == 0"
                  >供冷</span
                >
                <span v-if="projectOverallSettings.operatingMode == 1"
                  >供暖</span
                >
                <span v-if="projectOverallSettings.operatingMode == 2"
                  >通风</span
                >
              </a-descriptions-item>
              <a-descriptions-item label="室外温度	">
                {{ freshAirFanValue.outdoorTemperature }}℃</a-descriptions-item
              >
              <a-descriptions-item label="室外湿度	">
                {{ freshAirFanValue.outdoorHumidity }}%</a-descriptions-item
              >
              <a-descriptions-item label="新风机地址">
                {{ freshAirFaninfo.freshAirFanAdress }}</a-descriptions-item
              >
              <a-descriptions-item label="新风机型号">
                {{
                  freshAirFaninfo.freshAirFanType == 1 ? 'ivy500' : 'ivy350'
                }}</a-descriptions-item
              >
            </a-descriptions>
            <a-table
              class="margin-top-10"
              :columns="filteredColumns"
              :dataSource="scAirValve"
              :pagination="false"
              bordered
            >
              <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'roomName'">
                    <a-tooltip>
                   <template #title>传感器编号：{{record.sensorNo}}</template>
                  {{ record.roomName }}
                  </a-tooltip>
                </template>
                <template v-if="column.key === 'setTemperature'">
                  {{ record.setTemperature }}℃
                </template>
                <template v-if="column.key === 'currentTemperature'">
                  {{ record.currentTemperature }}℃
                </template>
                <template v-if="column.key === 'setHumidity'">
                  {{ record.setHumidity }}%
                </template>
                <template v-if="column.key === 'currentHumidity'">
                  {{ record.currentHumidity }}%
                </template>
                <template v-if="column.key === 'currentDewPoint'">
                  {{ record.currentDewPoint }}
                </template>

                <template v-if="column.key === 'currentDryBulbTemperature'">
                  {{ record.currentDryBulbTemperature }}℃
                </template>
                <template v-if="column.key === ' '">
                  {{ record.getThermalValveStatus ? '开' : '关' }}
                </template>
                <template v-if="column.key === 'getThermalValveStatus1'">
                  {{ record.getThermalValveStatus ? '开' : '关' }}
                </template>
                <template v-if="column.key === 'getAirValveStatus'">
                 <a-tooltip>
                   <template #title>风阀DO端口：{{record.airValveDO}}</template>
                  {{ record.getThermalValveStatus ? '开' : '关' }}
                  </a-tooltip>
                </template>

                <template v-if="column.key === 'operation'">
                  <a @click="scavsetting(record)">设置</a>
                </template>
              </template>
            </a-table>
            <div v-if="settiingshow" class="bg-color-white padding-top-10">
              <a-card :title="freshAirFaninfo.freshAirFanName + '设置'">
                <div class="text-font-18 padding-bottom-10">基础设定</div>
                <a-row class="margin-top-10" :gutter="16">
                  <a-col :span="8">
                    <span class="text-font-16 padding-20"
                      >电源状态(DO端口：{{
                        freshAirFaninfo.freshAirFanPowerSupplyDO
                      }})</span
                    >
                    <a-radio-group
                      v-model:value="freshAirFanValue.freshAirFanStatus"
                      name="freshAirFanStatus"
                    >
                      <a-radio :value="0">关</a-radio>
                      <a-radio :value="1">开</a-radio>
                    </a-radio-group>
                  </a-col>
                  <a-col :span="8">
                    <span class="text-font-16 padding-right-20">
                      高温泵(DO端口：{{
                        highTemperaturePump.highTemperaturePumpDO
                      }})
                    </span>
                    <a-radio-group
                      v-model:value="
                        highTemperaturePump.setHighTemperaturePumpStatus
                      "
                      name="setHighTemperaturePumpStatus"
                    >
                      <a-radio :value="0">关闭</a-radio>
                      <a-radio :value="1">打开</a-radio>
                      <a-radio :value="2">自动</a-radio>
                    </a-radio-group>
                  </a-col>
                  <a-col :span="8">
                    <span class="text-font-16 padding-right-20">
                      新风机运行模式
                    </span>
                    <a-select
                      style="width: 100px"
                      v-model:value="freshAirFanValue.operatingMode"
                      :options="fafOperatingModedata"
                      placeholder="请选择"
                    >
                    </a-select>
                  </a-col>
                </a-row>
                <div style="text-align: right; margin: 10px 20px">
                  <a-button type="primary" @click="basicSettings"
                    >保存</a-button
                  >
                </div>
                <a-divider style="height: 2px; background-color: #f5f5f5" />
                <a-card
                  v-if="projectOverallSettings.operatingMode == 0"
                  title="供冷模式"
                >
                  <table class="table-class">
                    <tr>
                      <th>模式</th>
                      <th>名称</th>
                      <th>送风风量</th>
                      <th>排风风量</th>
                      <th>送风温度</th>
                      <th>送风露点</th>
                    </tr>
                    <tr
                      v-for="(item, index) in freshAirFanOperatingModegl"
                      :key="index"
                    >
                      <td>{{ item.mode }}</td>
                      <td>{{ item.name }}</td>
                      <td>
                        <a-input-number
                          v-model:value="item.supplyAirVolume"
                          :min="
                            freshAirFaninfo.freshAirFanType == 1 ? 220 : 190
                          "
                          :step="10"
                          :max="500"
                        ></a-input-number>
                      </td>
                      <td>
                        <a-input-number
                          v-model:value="item.exhaustAirVolume"
                          :min="
                            freshAirFaninfo.freshAirFanType == 1 ? 180 : 150
                          "
                          :step="10"
                          :max="500"
                        ></a-input-number>
                      </td>
                      <td>
                        <a-input-number
                          v-if="item.mode != 'D'"
                          v-model:value="item.supplyAirTemperature"
                          :min="10"
                          :step="0.5"
                          :max="30"
                        ></a-input-number>
                      </td>
                      <td>
                        <a-input-number
                          v-if="item.mode !== 'D' && item.mode !== 'C'"
                          v-model:value="item.supplyAirDewPoint"
                          :min="5"
                          :step="0.5"
                          :max="15"
                        ></a-input-number>
                      </td>
                    </tr>
                  </table>
                </a-card>
                <a-card
                  v-if="projectOverallSettings.operatingMode == 1"
                  title="供暖模式"
                >
                  <table class="table-class">
                    <tr>
                      <th>模式</th>
                      <th>名称</th>
                      <th>送风风量</th>
                      <th>排风风量</th>
                      <th>送风温度</th>
                      <th>加湿露点</th>
                    </tr>
                    <tr
                      v-for="(item, index) in freshAirFanOperatingModegn"
                      :key="index"
                    >
                      <td>{{ item.mode }}</td>
                      <td>{{ item.name }}</td>
                      <td>
                        <a-input-number
                          v-model:value="item.supplyAirVolume"
                          :min="
                            freshAirFaninfo.freshAirFanType == 1 ? 220 : 190
                          "
                          :step="10"
                          :max="500"
                        ></a-input-number>
                      </td>
                      <td>
                        <a-input-number
                          v-model:value="item.exhaustAirVolume"
                          :min="
                            freshAirFaninfo.freshAirFanType == 1 ? 180 : 150
                          "
                          :step="10"
                          :max="1000"
                        ></a-input-number>
                      </td>
                      <td>
                        <a-input-number
                          v-if="item.mode != 'D'"
                          v-model:value="item.supplyAirTemperature"
                          :min="15"
                          :step="0.5"
                          :max="35"
                        ></a-input-number>
                      </td>
                      <td>
                        <a-input-number
                          v-if="item.mode !== 'D' && item.mode != 'A'"
                          v-model:value="item.supplyAirDewPoint"
                          :min="0"
                          :step="0.5"
                          :max="100"
                        ></a-input-number>
                      </td>
                    </tr>
                  </table>
                </a-card>
                <div
                  v-if="projectOverallSettings.operatingMode != 2"
                  style="text-align: right; margin: 10px 20px"
                >
                  <a-button type="primary" @click="glGnSettings">保存</a-button>
                </div>
              </a-card>
              <div style="text-align: right; margin: 10px">
                <a-space>
                  <a-button danger @click="settiingshow = false">关闭</a-button>
                </a-space>
              </div>
            </div>

            <div
              v-if="freshAirAreaParametersshow"
              class="bg-color-white padding-top-10"
            >
              <a-card
                v-if="projectOverallSettings.operatingMode == 0"
                :title="freshAirFaninfo.freshAirFanName + '的运行参数'"
              >
                <a-row :gutter="16">
                  <a-col :span="24">
                    <a-descriptions
                      title="逻辑计算"
                      layout=""
                      bordered
                      :column="2"
                      :labelStyle="{ width: '200px' }"
                    >
                      <a-descriptions-item label="新风机状态">
                        {{ freshAirFanValue.freshAirFanStatus ? '开' : '关' }}
                      </a-descriptions-item>
                      <a-descriptions-item label="新风机模式">
                        {{
                          freshAirFanValue.operatingMode == 0
                            ? '自动_'
                            : '手动_'
                        }}
                        <span
                          v-html="
                            dictTemplate.tabletempInt(
                              'fafOperatingMode',
                              freshAirFanValue.getOperatingMode,
                            )
                          "
                        ></span>
                      </a-descriptions-item>

                      <a-descriptions-item label="送风风量">
                        {{ freshAirFanValue.getSupplyAirVolume }}m³/h
                      </a-descriptions-item>
                      <a-descriptions-item label="排风风量">
                        {{ freshAirFanValue.getExhaustAirVolume }}m³/h
                      </a-descriptions-item>
                      <a-descriptions-item
                        label="送风温度"
                        v-if="freshAirFaninfo.getOperatingMode != 4"
                      >
                        {{ freshAirFanValue.getSupplyAirTemperature }}℃
                      </a-descriptions-item>
                      <a-descriptions-item
                        label="送风露点"
                        v-if="
                          freshAirFaninfo.getOperatingMode == 1 ||
                          freshAirFanValue.getOperatingMode == 2
                        "
                      >
                        {{ freshAirFanValue.getSupplyAirDewPoint }}
                      </a-descriptions-item>
                      <a-descriptions-item label="高温泵状态">
                        {{
                          highTemperaturePump.setHighTemperaturePumpStatus
                            ? '开'
                            : '关'
                        }}
                      </a-descriptions-item>
                      <a-descriptions-item label="压缩机状态">
                        {{
                          freshAirFanValue.getOperatingMode == 1 ||
                          freshAirFanValue.getOperatingMode == 2
                            ? '开'
                            : '关'
                        }}
                      </a-descriptions-item>
                    </a-descriptions>
                    <a-divider style="height: 2px; background-color: #f5f5f5" />
                    <a-descriptions
                      title="实际反馈"
                      layout=""
                      bordered
                      :labelStyle="{ width: '200px' }"
                    >
                      <a-descriptions-item label="新风机模式">
                        <span
                          v-html="
                            dictTemplate.tabletempInt(
                              'fafOperatingMode',
                              freshAirFanValue.getOperatingMode,
                            )
                          "
                        ></span>
                      </a-descriptions-item>

                      <a-descriptions-item label="蒸发器入口温度">
                        {{ freshAirFanValue.evaporatorInletTemperature }}℃
                      </a-descriptions-item>
                      <a-descriptions-item label="送风温度">
                        {{ freshAirFanValue.supplyAirTemperature }}℃
                      </a-descriptions-item>

                      <a-descriptions-item label="送风风量">
                        {{ freshAirFanValue.inletAirFlow }}m³/h
                      </a-descriptions-item>
                      <a-descriptions-item label="蒸发器回气温度">
                        {{ freshAirFanValue.evaporatorReturnTemperature }}℃
                      </a-descriptions-item>
                      <a-descriptions-item label="送风露点">
                        {{ freshAirFanValue.dewPointTemperature }}℃
                      </a-descriptions-item>
                      <a-descriptions-item label="排风风量">
                        {{ freshAirFanValue.exhaustAirFlow }}m³/h
                      </a-descriptions-item>
                      <a-descriptions-item label="冷凝温度">
                        {{ freshAirFanValue.condensingTemperature }}℃
                      </a-descriptions-item>
                      <a-descriptions-item label="水阀开度">
                        {{ freshAirFanValue.waterValveOpening }}%
                      </a-descriptions-item>

                       <a-descriptions-item label="送风转速">
                        {{ freshAirFanValue.supplyFanSpeed }}m³/h
                      </a-descriptions-item>
                      <a-descriptions-item label="压缩机温度">
                        {{ freshAirFanValue.compressorTemperatureNTC3 }}℃
                      </a-descriptions-item>
                      <a-descriptions-item label="报警1">
                        <span v-if="freshAirFanValue.exception1!=0" class="text-color-error"><WarningOutlined /> {{freshAirFanValue.exception1Mgs}}</span>
                      </a-descriptions-item>
                        <a-descriptions-item label="排风转速">
                        {{ freshAirFanValue.exhaustFanSpeed }}m³/h
                      </a-descriptions-item>
                      <a-descriptions-item label="P8电子膨胀阀开度">
                        {{ freshAirFanValue.p8ElectronicExpansionValve }}%
                      </a-descriptions-item>
                      <a-descriptions-item label="报警2">
                        {{ freshAirFanValue.exception2 }}
                      </a-descriptions-item>
                      <a-descriptions-item label="高温泵状态">
                        {{
                          highTemperaturePump.getHighTemperaturePumpStatus
                            ? '开'
                            : '关'
                        }}
                      </a-descriptions-item>

                      <a-descriptions-item label="P9电子膨胀阀开度">
                        {{ freshAirFanValue.p9ElectronicExpansionValve }}%
                      </a-descriptions-item>

                      <a-descriptions-item label="报警3">
                        {{ freshAirFanValue.exception2 }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-col>
                </a-row>
              </a-card>
              <a-card
                v-if="projectOverallSettings.operatingMode == 1"
                :title="freshAirFaninfo.freshAirFanName + '的运行参数'"
              >
                <a-row :gutter="16">
                  <a-col :span="24">
                    <a-descriptions
                      title="逻辑计算"
                      layout=""
                      bordered
                      :column="2"
                      :labelStyle="{ width: '200px' }"
                    >
                      <a-descriptions-item label="新风机状态">
                        {{ freshAirFanValue.freshAirFanStatus ? '开' : '关' }}
                      </a-descriptions-item>
                      <a-descriptions-item label="新风机模式">
                        <span
                          v-html="
                            dictTemplate.tabletempInt(
                              'fafOperatingMode',
                              freshAirFanValue.getOperatingMode,
                            )
                          "
                        ></span>
                      </a-descriptions-item>

                      <a-descriptions-item label="排风风量">
                        {{ freshAirFanValue.getExhaustAirVolume }}m³/h
                      </a-descriptions-item>
                      <a-descriptions-item label="送风风量">
                        {{ freshAirFanValue.getSupplyAirVolume }}m³/h
                      </a-descriptions-item>
                      <a-descriptions-item label="送风温度">
                        {{ freshAirFanValue.getSupplyAirTemperature }}℃
                      </a-descriptions-item>
                    </a-descriptions>
                    <a-divider style="height: 2px; background-color: #f5f5f5" />
                    <a-descriptions
                      title="实际反馈"
                      layout=""
                      bordered
                      :column="2"
                      :labelStyle="{ width: '200px' }"
                    >
                      <a-descriptions-item label="新风机模式">
                        <span
                          v-html="
                            dictTemplate.tabletempInt(
                              'fafOperatingMode',
                              freshAirFanValue.operatingMode,
                            )
                          "
                        ></span>
                      </a-descriptions-item>

                      <a-descriptions-item label="送风温度">
                        {{ freshAirFanValue.supplyAirTemperature }}℃
                      </a-descriptions-item>

                      <a-descriptions-item label="送风风量">
                        {{ freshAirFanValue.inletAirFlow }}m³/h
                      </a-descriptions-item>

                      <a-descriptions-item label="水阀开度">
                        {{ freshAirFanValue.waterValveOpening }}%
                      </a-descriptions-item>

                      <a-descriptions-item label="排风风量">
                        {{ freshAirFanValue.exhaustAirFlow }}m³/h
                      </a-descriptions-item>
                      <a-descriptions-item label="报警1">
                        {{ freshAirFanValue.exception1 }}
                      </a-descriptions-item>
             <a-descriptions-item label="送风转速">
                        {{ freshAirFanValue.supplyFanSpeed }}m³/h
                      </a-descriptions-item>


                      <a-descriptions-item label="报警2">
                        {{ freshAirFanValue.exception2 }}
                      </a-descriptions-item>
                        <a-descriptions-item label="排风转速">
                        {{ freshAirFanValue.exhaustFanSpeed }}m³/h
                      </a-descriptions-item>
                      <a-descriptions-item label="报警3">
                        {{ freshAirFanValue.exception2 }}
                      </a-descriptions-item>
                      <a-descriptions-item label="高温泵状态">
                        {{
                          highTemperaturePump.getHighTemperaturePumpStatus
                            ? '开'
                            : '关'
                        }}
                      </a-descriptions-item>
                    </a-descriptions>
                  </a-col>
                </a-row>
              </a-card>

              <div style="text-align: right; margin: 10px">
                <a-space>
                  <a-button danger @click="freshAirAreaParametersshow = false"
                    >关闭</a-button
                  >
                </a-space>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
      <div v-else><a-empty /></div>
    </a-card>
  </div>
  <a-modal
    v-model:visible="scavsettingshow"
    title="房间设置"
    width="500px"
    @ok="scSettings"
  >
    <div class="padding-tb-20">
      <a-form :model="scavinfo" ref="formRef" layout="">
        <a-form-item label="风阀状态" name="setAirValveStatus">
          <a-radio-group
            v-model:value="scavinfo.setAirValveStatus"
            name="radioGroup"
          >
            <a-radio :value="0">关闭</a-radio>
            <a-radio :value="1">开启</a-radio>
            <a-radio :value="2">自动</a-radio>
          </a-radio-group>
        </a-form-item>
        <div class="padding-bottom-30" v-if="scavinfo.setAirValveStatus == 2">
          <a-row :gutter="16">
            <a-col :span="12">
              <span class="text-font-14 padding-right-20">开启时间：</span>
              <a-time-picker
                v-model:value="onAirValveTime"
                format="HH:mm"
                @change="onOnAirValveTimeChange"
              />
            </a-col>
            <a-col :span="12">
              <span class="text-font-14 padding-right-20">关闭时间：</span>
              <a-time-picker
                v-model:value="offAirValveTime"
                format="HH:mm"
                @change="onOffAirValveTimeChange"
              />
            </a-col>
          </a-row>
        </div>
        <a-form-item label="设定温度" name="setTemperature">
          <a-input-number
            style="width: 120px"
            :min="0"
            :max="100"
            :step="0.5"
            addon-after="℃"
            v-model:value="scavinfo.setTemperature"
            placeholder="请输入"
          />
        </a-form-item>
        <a-form-item label="设定湿度" name="setHumidity">
          <a-input-number
            :min="0"
            :max="100"
            :step="0.5"
            style="width: 120px"
            addon-after="℃"
            v-model:value="scavinfo.setHumidity"
            placeholder="请输入"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>
<script setup>
import {
  defineProps,
  defineEmits,
  ref,
  reactive,
  onMounted,
  onBeforeUnmount,
  inject,
  computed,
} from 'vue'
import freshAirFan from '@/api/methods/freshAirFan'
import DateUtil from '../../utils/dateUtil'
import dictTemplate from '@/utils/dictTemplate'
import { dictStore } from '@/stores/dict'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
const dict = dictStore()
const userInfo = inject('userInfo')
var fafOperatingModedata = dict.getDictItems('fafOperatingMode')
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
const projectId = props.recordwhere.projectId
const freshAirFanlist = ref([])
const freshAirFaninfo = ref({})
const freshAirFanValue = ref({})
const projectOverallSettings = ref({})
const highTemperaturePump = ref({})
const settiingshow = ref(false)
const freshAirAreaParametersshow = ref(false)
const freshAirFanOperatingModegl = ref([])
const freshAirFanOperatingModegn = ref([])
const scAirValve = ref([])
const onAirValveTime = ref('')
const offAirValveTime = ref('')
const init = () => {
  freshAirFan.getByProjectIdList({ projectId }).then(res => {
    freshAirFanlist.value = res.data
    if (freshAirFanlist.value.length > 0) {
      getInfo(res.data[0].id)
    }
  })
}

init()
const selectedIndex = ref(0) // 默认选中第一个项
const handleItemClick = (item, index) => {
  selectedIndex.value = index
  freshAirAreaParametersshow.value = false
  settiingshow.value = false
  getInfo(item.id)
}
//运行模式
const yxmodel = ref('')
//系统状态
const systatus = ref('')
const getInfo = id => {
  freshAirFan.getFaFProjectOverallSetting({ freshAirFanId: id }).then(res => {
    freshAirFaninfo.value = res.data.freshAirFan
    freshAirFanValue.value = res.data.freshAirFanValue
    projectOverallSettings.value = res.data.projectOverallSettings
    highTemperaturePump.value = res.data.highTemperaturePump
    var freshAirFanOperatingModelist = res.data.freshAirFanOperatingModelist
    freshAirFanOperatingModegl.value = freshAirFanOperatingModelist.filter(
      x => x.modeType == 0,
    )
    freshAirFanOperatingModegn.value = freshAirFanOperatingModelist.filter(
      x => x.modeType == 1,
    )
    scAirValve.value = res.data.scAirValve
    yxmodel.value =
      res.data.projectOverallSettings.seasonMode == 0 ? '手动' : '自动'
    systatus.value =
      res.data.projectOverallSettings.workingCondition == 0
        ? '关闭'
        : res.data.projectOverallSettings.workingCondition == 1
          ? '运行'
          : res.data.projectOverallSettings.workingCondition == 2
            ? res.data.projectOverallSettings.automaticWorking == 1
              ? '值班'
              : res.data.projectOverallSettings.automaticWorking == 2
                ? '值班'
                : '未知'
            : '未知'
  })
}
const columns = [
  {
    title: '房间名称',
    key: 'roomName',
  },
  {
    title: '设定作用温度',
    key: 'setTemperature',
  },
  {
    title: '实际作用温度',
    key: 'currentTemperature',
  },
  {
    title: '设定相对湿度',
    key: 'setHumidity',
  },
  {
    title: '实际相对湿度',
    key: 'currentHumidity',
  },
  {
    title: '实际干球温度',
    key: 'currentDryBulbTemperature',
  },
  {
    title: '实际露点',
    key: 'currentDewPoint',
  },
  {
    title: '除湿需求',
    key: 'getThermalValveStatus',
  },
  {
    title: '加湿需求',
    key: 'getThermalValveStatus1',
  },
  {
    title: '风阀状态',
    key: 'getAirValveStatus',
  },
  {
    title: '操作',
    key: 'operation',
    width: 100,
  },
]
// 计算属性，根据 activeKey 动态决定列的显示
const filteredColumns = computed(() => {
  // 当 activeKey 为 '2' 时，隐藏 'coldHeatSourcesMode' 列
  if (projectOverallSettings.value.operatingMode == 1) {
    return columns.filter(col => col.key !== 'getThermalValveStatus1')
  }
  if (projectOverallSettings.value.operatingMode == 0) {
    return columns.filter(col => col.key !== 'getThermalValveStatus')
  }
  return columns
})

//房间设置
const scavsettingshow = ref(false)
const scavinfo = reactive({
  sensorConfigId: '',
  airValveId: '',
  setTemperature: 0,
  setHumidity: 0,
  setAirValveStatus: 0,
  onAirValveTime: '08:00',
  offAirValveTime: '20:00',
})
const scavsetting = record => {
  if (!userInfo.value.isConfig) {
    message.error('暂无配置权限，无法进行操作')
    return
  }
  scavsettingshow.value = true
  scavinfo.sensorConfigId = record.sensorConfigId
  scavinfo.airValveId = record.airValveId
  scavinfo.setTemperature = record.setTemperature
  scavinfo.setHumidity = record.setHumidity
  scavinfo.setAirValveStatus = record.setAirValveStatus
  onAirValveTime.value = dayjs(record.onAirValveTime, 'HH:mm')
  offAirValveTime.value = dayjs(record.offAirValveTime, 'HH:mm')
  scavinfo.onAirValveTime = record.onAirValveTime
  scavinfo.offAirValveTime = record.offAirValveTime
}

///打开运行参数
const onCoolingParameters = () => {
  freshAirAreaParametersshow.value = true
  settiingshow.value = false
}

//基础设定保存
const basicSettings = () => {
  var data = {
    freshAirFanValueId: freshAirFanValue.value.id,
    freshAirFanStatus: freshAirFanValue.value.freshAirFanStatus,
    operatingMode: freshAirFanValue.value.operatingMode,
    highTemperaturePumpId: freshAirFaninfo.value.highTemperaturePumpId,
    setHighTemperaturePumpStatus:
      highTemperaturePump.value.setHighTemperaturePumpStatus,
  }

  freshAirFan.basicSettings(data).then(res => {
    message.success('成功', 1, () => {
      // onClose()
      // emit('updateData') // 触发事件
    })
  })
}
//供冷供暖模式设定
const glGnSettings = () => {
  var combinedList = [
    ...freshAirFanOperatingModegl.value,
    ...freshAirFanOperatingModegn.value,
  ]
  freshAirFan.glGnSettings(combinedList).then(res => {
    message.success('成功', 1, () => {
      // onClose()
      // emit('updateData') // 触发事件
    })
  })
}
//房间设置
const scSettings = () => {
  console.log(scavinfo)
  freshAirFan.scSettings(scavinfo).then(res => {
    message.success('成功', 1, () => {
      scavsettingshow.value = false
      getInfo(freshAirFanlist.value[selectedIndex.value].id)
    })
  })
}

const onOnAirValveTimeChange = (time, timeString) => {
  scavinfo.onAirValveTime = timeString
}
const onOffAirValveTimeChange = (time, timeString) => {
  scavinfo.offAirValveTime = timeString
}
// 定义定时器变量
let timer = null
// 启动定时器
const startTimer = () => {
  timer = setInterval(() => {
    getInfo(freshAirFanlist.value[selectedIndex.value].id)
  }, 5000)
}

// 在组件挂载后启动定时器
onMounted(() => {
  //startTimer()
})

// 在组件销毁前清除定时器
onBeforeUnmount(() => {
  if (timer) {
    //clearInterval(timer)
  }
})
// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close'], ['change-active'])
const changeActive = value => {
  emit('change-active', value)
}
</script>
<style scoped>
.listitem {
  padding: 15px;
  border: 1px solid #f0f0f0;
  cursor: pointer;
}
.listitemselected {
  background-color: #1677ff;
  color: white;
}
.table-class {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  table-layout: fixed; /* 固定布局，确保宽度生效 */
}
.table-class th,
.table-class td {
  border: 1px solid #ddd;
  padding: 12px;
  text-align: left;
  word-wrap: break-word; /* 避免超出内容影响宽度 */
}
.table-class th {
  background-color: #ffffff;
}
.table-class th:first-child,
.table-class td:first-child {
  width: 200px; /* 第一列的宽度设置为200像素 */
}
</style>
