import { post, get } from '@/api/request'

const statistics = {
  //统计室外温度
  outdoorTemperature(params) {
    return get('statistics/outdoorTemperature', params)
  },
  //统计室外湿度
  outdoorHumidity(params) {
    return get('statistics/outdoorHumidity', params)
  },
  //统计室内温度
  temperature(params) {
    return get('statistics/temperature', params)
  },
  //统计室内湿度
  humidity(params) {
    return get('statistics/humidity', params)
  },
  //统计露点
  dewPoint(params) {
    return get('statistics/dewPoint', params)
  },
    //查询统计
    gettotal(params) {
      return get('statistics/total', params,true)
    }
}
//
export default statistics
