<template>
  <form-table
    :formState="formState"
    :columns="columns"
    modulePath="sysDict"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    @edit="edit"
    ref="childRef"
  >
    <template #table-toolbar="{ record }">
      <a-button @click="refreshCache" size="middle"
        ><RedoOutlined />刷新缓存</a-button
      >
    </template>

    <template #custom-operation="{ record }">
      <a-button type="link" size="small" @click="configuration(record)"
        >配置字典项</a-button
      >
    </template>
    <!-- 可以通过插槽自定义单元格 -->
    <template #custom-isEnable="{ record }">
      <a-switch
        v-model:checked="record.isEnable"
        checked-children="是"
        un-checked-children="否"
        @change="handleSwitchChange(record)"
      />
    </template>
  </form-table>
  <!-- 编辑 -->
  <SysDictEdit
    :editTitle="editTitle"
    :open="editopen"
    :formInfo="formInfo"
    @close="editopen = false"
    @updateData="refreshData"
  >
  </SysDictEdit>

  <!-- 配置字典项 -->

  <a-modal
    v-model:open="configurationopen"
    :title="configurationtitle"
    width="70%"
    wrap-class-name="full-modal"
    :destroyOnClose="true"
  >
    <SysDictItemPage :recordwhere="recordwhere" ref="childPageRef">
    </SysDictItemPage>
  </a-modal>
</template>
<script setup>
import { ref, reactive, toRaw, nextTick } from 'vue'
import FormTable from '../../components/FormTable.vue'
import SysDictEdit from '../../components/edit/SysDictEdit.vue'
import SysDictItemPage from '../../components/pages/SysDictItemPage.vue'
import sysDict from '@/api/methods/sysDict'
import { message } from 'ant-design-vue'
import { dictStore } from '@/stores/dict'
const formState = ref({
  name: { label: '字典名称', value: '', type: 'text' },
  type: { label: '字典类型', value: '', type: 'text' },
  Enable: {
    label: '是否启用',
    value: null,
    type: 'select',
    data: [
      { label: '是', value: true },
      { label: '否', value: false },
    ],
  },
})
const columns = ref([
  {
    title: '序号',
    key: 'num',
  },
  {
    title: '字典名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '字典类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '是否启用',
    key: 'isEnable',
    dataIndex: 'isEnable',
  },
  {
    title: '备注',
    key: 'remark',
    dataIndex: 'remark',
  },

  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
    width: 200,
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)

//编辑
const defaultformInfo = {
  id: 0,
  name: '',
  type: '',
  isEnable: true,
  remark: '',
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editopen = ref(false)
const editTitle = ref('新增')
//新增
const edit = record => {
  // 触发自定义事件，父组件会监听这个事件
  if (record.id) {
    editTitle.value = '修改'
    sysDict.get({ id: record.id }).then(res => {
      Object.assign(formInfo, res.data)
      editopen.value = true
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
    editopen.value = true
  }
}
const refreshData = () => {
  childRef.value.tableLoad()
}

//修改是否启用
const handleSwitchChange = record => {
  var data = {
    id: record.id,
    isEnabled: record.isEnable,
  }
  sysDict.updateIsEnabled(data).then(() => {
    childRef.value.tableLoad()
  })
}
//刷新缓存
const refreshCache = () => {
  const dict = dictStore()
  dict.$reset()
  dict.initDictList()
  message.success('刷新缓存成功', 1)
}
//---------------------------配置字典项----------------------------------
const configurationopen = ref(false)
const configurationtitle = ref('配置字典项')
const recordwhere = ref({})
const childPageRef = ref(null)
const configuration = record => {
  configurationtitle.value = '配置字典项-' + record.name
  recordwhere.value = { dictId: record.id }
  configurationopen.value = true
  nextTick(() => {
    if (childPageRef.value) {
      childPageRef.value.refreshData() // 调用子组件暴露的方法
    }
  })
}
</script>

<style></style>
