<template>
  <a-drawer
    :title="editTitle"
    :width="540"
    :open="open"
    :body-style="{ paddingBottom: '80px' }"
    :footer-style="{ textAlign: 'right' }"
    @close="onClose"
  >
    <a-form :model="formInfo" ref="formRef" layout="vertical">
      <a-form-item
        label="字典名称"
        name="name"
        :rules="[{ required: true, message: '字典名称不能为空' }]"
      >
        <a-input v-model:value="formInfo.name" placeholder="请输入" />
      </a-form-item>
      <a-form-item
        label="字典类型"
        name="type"
        :rules="[{ required: true, message: '字典类型不能为空' }]"
      >
        <a-input v-model:value="formInfo.type" placeholder="请输入" />
      </a-form-item>
      <a-form-item label="是否启用" name="isEnable">
        <a-switch v-model:checked="formInfo.isEnable" />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea v-model:value="formInfo.remark" />
      </a-form-item>
    </a-form>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onSave">保存</a-button>
        <a-button @click="() => formRef.resetFields()">重置</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>
<script setup>
import { defineProps, defineEmits, ref } from 'vue'
import { message } from 'ant-design-vue'
import sysDict from '@/api/methods/sysDict'
const props = defineProps({
  editTitle: {
    type: String,
    default: '新增',
  },
  open: {
    type: Boolean,
    required: true,
  },
  formInfo: {
    type: Object,
    required: true,
  },
})
const formRef = ref(null)
const onSave = () => {
  formRef.value.validate().then(() => {
    if (props.formInfo.id == 0) {
      sysDict.add(props.formInfo).then(() => {
        message.success('成功', 1)
        onClose()
        emit('updateData') // 触发事件
      })
    } else {
      sysDict.update(props.formInfo).then(() => {
        message.success('成功', 1)
        onClose()
        emit('updateData') // 触发事件
      })
    }
  })
}

// 定义 emits，用于触发关闭事件
const emit = defineEmits(['close'])
const onClose = () => {
  emit('close')
}
</script>
