<template>
  <div class="bg-color-white flex-row">
    <div class="flex-item-1">
      <a-tabs
        class="padding-left-20"
        v-model:activeKey="activeKey"
        @change="activeKeyChange"
      >
        <a-tab-pane key="1">
          <template #tab>
            <a-badge> 天棚区</a-badge>
          </template>
        </a-tab-pane>
        <a-tab-pane key="2" tab="地板区" force-render></a-tab-pane>
        <!-- <a-tab-pane key="3" tab="风盘区"></a-tab-pane> -->
      </a-tabs>
    </div>
    <div
      class="flex-item-9 text-color-red"
      style="padding-bottom: 18px; padding-left: 20px"
    >
      （辐射区最大支持16个分区）
    </div>
  </div>

  <form-table
    :columns="columns"
    modulePath="RadiationZone"
    method="get"
    pageAction="getList"
    :where="localRecordwhere"
    :ftableEdit="true"
    :ftableAdd="true"
    :ftableDelete="true"
    :page="false"
    @edit="edit"
    ref="childRef"
  >
    <template #custom-zoneNo="{ record }"> {{ record.zoneNo }}区 </template>
  </form-table>
  <!-- </a-card> -->
  <!-- 编辑 -->
  <RadiationZoneEdit
    :editTitle="editTitle"
    :open="editopen"
    :formInfo="formInfo"
    @close="editopen = false"
    @updateData="refreshData"
  />
</template>
<script setup>
import { ref, defineProps, reactive, nextTick, inject } from 'vue'
import FormTable from '../../FormTable.vue'
import RadiationZoneEdit from '../../edit/RadiationZoneEdit.vue'
import radiationZone from '@/api/methods/radiationZone'
// 注入父组件的方法
const getproject = inject('getproject')
const props = defineProps({
  recordwhere: {
    type: Object,
    default: {},
  },
})
// 创建一个响应式的本地副本
const localRecordwhere = ref({ ...props.recordwhere })
const activeKey = ref('1')
const columns = ref([
  {
    title: '分区编号',
    key: 'zoneNo',
  },
  {
    title: '分区名称',
    dataIndex: 'zoneName',
  },
  {
    title: '水泵名称',
    dataIndex: 'waterPumpName',
  },
  {
    title: '水泵端口',
    dataIndex: 'waterPumpDO',
  },
  {
    title: '供水水温端口',
    dataIndex: 'supplyWaterTemperaturePort',
  },
  {
    title: '回水水温端口',
    dataIndex: 'returnWaterTemperaturePort',
  },
  {
    title: '混水阀端口',
    dataIndex: 'mixingValvePort',
  },
  {
    title: '接收混水阀信息端口',
    dataIndex: 'mixingValveInfoPort',
  },
  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
    width: 200,
  },
])
const childRef = ref(null)

const activeKeyChange = key => {
  localRecordwhere.value = {
    projectId: props.recordwhere.projectId,
    zoneType: key,
  }
  nextTick(() => {
    refreshData()
  })
}
//编辑
const defaultformInfo = {
  id: '',
  zoneNo: null,
  projectId: localRecordwhere.value.projectId,
  zoneType: localRecordwhere.value.zoneType,
  zoneName: '',
  waterPumpName: '',
  waterPumpDO: 0,
  supplyWaterTemperaturePort: 0,
  returnWaterTemperaturePort: 0,
  mixingValvePort: 0,
  mixingValveInfoPort: 0,
  logicalProperty: 1,
  mixedOperationMode: [],
  roomOptions: [], //房间项
  selectedRooms: [], //房间选中项
  thermoelectricValve: [],
  zoneNoOptions: [],
}
// 使用 reactive 定义表单状态
const formInfo = reactive({ ...defaultformInfo })
const editopen = ref(false)
const editTitle = ref('新增')
const edit = record => {
  radiationZone
    .getNoSensorConfigList({
      projectId: localRecordwhere.value.projectId,
      zoneType: localRecordwhere.value.zoneType,
      radiationZoneId: record.id,
    })
    .then(res => {
      formInfo.roomOptions = res.data
      formInfo.selectedRooms = res.data
        .filter(item => item.checked)
        .map(item => item.value)
    })
  radiationZone
    .getZoneNoList({
      projectId: localRecordwhere.value.projectId,
    })
    .then(res => {
      formInfo.zoneNoOptions = res.data
    })

  // 触发自定义事件，父组件会监听这个事件
  if (record.id) {
    editTitle.value = '修改'
    radiationZone.get({ id: record.id }).then(res => {
      Object.assign(formInfo, res.data.radiationZone)
      formInfo.thermoelectricValve = res.data.thermoelectricValve
      formInfo.zoneType = localRecordwhere.value.zoneType
      editopen.value = true
    })
  } else {
    editTitle.value = '新增'
    Object.assign(formInfo, defaultformInfo)
    var number = childRef.value.getDataSource().length + 1
    formInfo.zoneName =
      localRecordwhere.value.zoneType == '1'
        ? '天棚'
        : localRecordwhere.value.zoneType == '2'
          ? '地板'
          : '风盘'
    formInfo.waterPumpName = formInfo.zoneName + '水泵'
    formInfo.zoneType = localRecordwhere.value.zoneType
    editopen.value = true
  }
}

const refreshData = () => {
  childRef.value.tableLoad()
  getproject()
}
defineExpose({
  refreshData,
})
</script>
