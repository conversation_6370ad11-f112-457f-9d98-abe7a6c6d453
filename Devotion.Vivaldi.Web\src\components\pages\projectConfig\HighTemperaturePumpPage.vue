<template>
  <div class="padding-20">
    <a-form layout="inline" :model="formState">
      <a-form-item label="高温泵名称">
        <a-input
          v-model:value="formState.highTemperaturePumpName"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item label="高温泵端口">
        <a-input-number
          style="width: 100%"
          :min="0"
          :max="80"
          :precision="0"
          v-model:value="formState.highTemperaturePumpDO"
          placeholder="请输入"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="add">新增</a-button>
      </a-form-item>
    </a-form>
  </div>
  <a-table :dataSource="dataSource" :columns="columns" :pagination="false">
    <!-- <template v-slot:num="{ index }">
      <span>{{
        (pagination.current - 1) * pagination.pageSize + index + 1
      }}</span>
    </template> -->
    <template #bodyCell="{ column, text, record }">
      <template
        v-if="
          ['highTemperaturePumpName', 'highTemperaturePumpDO'].includes(
            column.dataIndex,
          )
        "
      >
        <div>
          <a-input
            v-if="editableData[record.key]"
            v-model:value="editableData[record.key][column.dataIndex]"
            style="margin: -5px 0"
          />
          <template v-else>
            {{ text }}
          </template>
        </div>
      </template>
      <template v-if="column.key === 'operation'">
        <a-button
          v-if="!editableData[record.key]"
          type="link"
          size="small"
          @click="edit(record.key)"
          >编辑</a-button
        >

        <a-button
          v-if="editableData[record.key]"
          type="link"
          size="small"
          @click="save(record.key)"
          >保存</a-button
        >
        <a-button
          v-if="editableData[record.key]"
          type="link"
          size="small"
          @click="cancel(record.key)"
          >取消</a-button
        >
        <a-button type="link" danger size="small" @click="tabelDelete(record)"
          >删除</a-button
        >
      </template>
    </template>
  </a-table>
</template>
<script setup>
import { ref, defineProps, reactive, toRaw, onMounted } from 'vue'
import highTemperaturePump from '@/api/methods/highTemperaturePump'
import { Modal, message } from 'ant-design-vue'
import { cloneDeep, max } from 'lodash-es'
const props = defineProps({
  projectId: String,
})
const formState = reactive({
  projectId: props.projectId,
  highTemperaturePumpName: '',
  highTemperaturePumpDO: '',
})
const columns = ref([
  //   {
  //     title: '序号',
  //     key: 'num',
  //     slots: { customRender: 'num' },
  //   },
  {
    title: '传感器名称',
    dataIndex: 'highTemperaturePumpName',
    key: 'highTemperaturePumpName',
  },
  {
    title: '端口号',
    dataIndex: 'highTemperaturePumpDO',
    key: 'highTemperaturePumpDO',
  },
  {
    title: '操作',
    key: 'operation',
    fixed: 'right',
  },
])
const dataSource = ref([])
const childRef = ref(null)

const add = () => {
  console.log(formState)
  if (formState.highTemperaturePumpName == '') {
    return message.error('请输入高温泵名称')
  }
  if (formState.highTemperaturePumpDO == null) {
    return message.error('请输入端口号')
  }
  if (formState.highTemperaturePumpDO == '') {
    return message.error('端口号不能为0，请输入正确')
  }

  highTemperaturePump.add(formState).then(() => {
    getList()
  })
}

const getList = () => {
  highTemperaturePump.getList({ projectId: props.projectId }).then(res => {
    dataSource.value = res.data
  })
}
const editableData = reactive({})
const edit = key => {
  editableData[key] = cloneDeep(
    dataSource.value.filter(item => key === item.key)[0],
  )
}
const save = key => {
  Object.assign(
    dataSource.value.filter(item => key === item.key)[0],
    editableData[key],
  )

  var info = editableData[key]
  if (info.highTemperaturePumpName == '') {
    return message.error('请输入高温泵名称')
  }
  if (info.highTemperaturePumpDO == null) {
    return message.error('请输入端口号')
  }
  if (info.highTemperaturePumpDO == '') {
    return message.error('端口号不能为0，请输入正确')
  }
  highTemperaturePump.update(info).then(() => {
    delete editableData[key]
    getList()
  })
}
const tabelDelete = record => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除该数据吗？',
    onOk() {
      highTemperaturePump.delete({ id: record.id }).then(res => {
        message.success({
          content: '成功',
          duration: 1,
          onClose: () => {
            getList()
          },
        })
      })
    },
  })
}
const cancel = key => {
  delete editableData[key]
  getList()
}
//

const refreshData = () => {
  getList()
}
defineExpose({
  refreshData,
})
</script>
